/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import { NavigationContainer } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { navigation } from './src/navigation/navigation';
import Screens from './src/screens';
import defaultConfig from '@tamagui/config/v3';
import { createTamagui, TamaguiProvider, View } from 'tamagui';

import { Amplify } from 'aws-amplify';
import amplifyconfig from './src/amplifyconfiguration.json';
import AuthProvider from './src/providers/AuthProvider';
import OnboardingProvider from './src/providers/OnboardingProvider';
import ApolloProvider from './src/providers/ApolloProvider';
import { StripeProvider } from '@stripe/stripe-react-native';
import env from 'react-native-config';
import UserInfoProvider from './src/providers/UserInfoProvider';
import AppProvider from './src/providers/AppProvider';
Amplify.configure(amplifyconfig);

function App(): React.JSX.Element {
	const uiConfig = createTamagui(defaultConfig);

	const [publishableKey, setPublishableKey] = useState('');

	const fetchPublishableKey = async () => {
		// const key = await fetchKey(); // fetch key from your server here
		if (env.PUBLISHABLE_KEY) setPublishableKey(env.PUBLISHABLE_KEY);
	};

	useEffect(() => {
		fetchPublishableKey();
	}, []);

	return (
		<AppProvider>
			<AuthProvider>
				<ApolloProvider>
					<UserInfoProvider>
						<StripeProvider
							publishableKey={publishableKey}
							merchantIdentifier="merchant.identifier" // required for Apple Pay
							urlScheme="your-url-scheme" // required for 3D Secure and bank redirects
						>
							<TamaguiProvider config={uiConfig}>
								<NavigationContainer ref={navigation}>
									<OnboardingProvider>
										<Screens />
									</OnboardingProvider>
								</NavigationContainer>
							</TamaguiProvider>
						</StripeProvider>
					</UserInfoProvider>
				</ApolloProvider>
			</AuthProvider>
		</AppProvider>
	);
}

export default App;
