{"version": "1", "cognitoConfig": {"identityPoolName": "easygarageapp1e4d0c47_identitypool_1e4d0c47", "allowUnauthenticatedIdentities": false, "resourceNameTruncated": "easyga1e4d0c47", "userPoolName": "easygarageapp1e4d0c47_userpool_1e4d0c47", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email"], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "easyga1e4d0c47_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "1e4d0c47", "resourceName": "easygarageapp1e4d0c47", "authSelections": "identityPoolAndUserPool", "useDefault": "defaultSocial", "hostedUI": true, "usernameAttributes": ["email"], "hostedUIDomainName": "easygarageapp1e4d0c47-1e4d0c47", "authProvidersUserPool": ["Google", "SignInWithApple"], "hostedUIProviderMeta": "[{\"ProviderName\":\"Google\",\"authorize_scopes\":\"openid email profile\",\"AttributeMapping\":{\"email\":\"email\",\"username\":\"sub\"}},{\"ProviderName\":\"SignInWithApple\",\"authorize_scopes\":\"email\",\"AttributeMapping\":{\"email\":\"email\"}}]", "userPoolGroupList": [], "serviceName": "Cognito", "usernameCaseSensitive": false, "useEnabledMfas": true, "authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "unauthRoleArn": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}, "breakCircularDependency": true, "dependsOn": [], "oAuthMetadata": "{\"AllowedOAuthFlows\":[\"code\"],\"AllowedOAuthScopes\":[\"phone\",\"email\",\"openid\",\"profile\",\"aws.cognito.signin.user.admin\"],\"CallbackURLs\":[\"easygarage://\"],\"LogoutURLs\":[\"easygarage://\"]}"}}