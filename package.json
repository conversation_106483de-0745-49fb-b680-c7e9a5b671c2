{"name": "easygarage", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios --simulator='iPhone 15 Plus'", "ios:pods": "cd ios && pod install && cd ..", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@apollo/client": "^3.11.8", "@aws-amplify/react-native": "^1.1.6", "@aws-amplify/rtn-web-browser": "^1.1.1", "@react-native-async-storage/async-storage": "^1.21.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/analytics": "^21.6.1", "@react-native-firebase/app": "^21.6.1", "@react-native-firebase/remote-config": "^21.6.1", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@stripe/stripe-react-native": "^0.40.0", "@tamagui/config": "^1.116.7", "aws-amplify": "^6.6.7", "formik": "^2.4.6", "lottie-react-native": "^7.0.0", "react": "18.3.1", "react-native": "0.75.4", "react-native-config": "^1.5.3", "react-native-confirmation-code-field": "^7.4.0", "react-native-gesture-handler": "^2.18.1", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-reanimated": "^3.15.0", "react-native-reanimated-carousel": "^4.0.0-alpha.12", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "^3.35.0", "react-native-timer-picker": "^2.0.0", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "tamagui": "^1.116.7", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.75.4", "@react-native/eslint-config": "0.75.4", "@react-native/metro-config": "0.75.4", "@react-native/typescript-config": "0.75.4", "@tsconfig/react-native": "^3.0.5", "@types/jest": "^29.5.13", "@types/react": "^18.3.11", "@types/react-native": "^0.73.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.6.3", "constructs": "^10.0.0", "esbuild": "^0.24.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "tsx": "^4.19.2", "typescript": "^5.0.0"}, "engines": {"node": "20.18.0"}, "engineStrict": true, "packageManager": "yarn@3.6.4"}