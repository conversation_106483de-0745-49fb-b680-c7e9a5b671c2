import { gql } from '@apollo/client';

export const GET_USER_INFO = gql`
	query Query($userId: String) {
		getUser(userId: $userId) {
			status
			message
			user {
				userId
				email
				acceptedTC
				userType
				# Client
				firstName
				lastName

				# Company
				companyName
				representativeFirstName
				representativeLastName
				subscriptionType
				country
				city
				street
				streetNumber
				postalCode
				state
				taxId

				# Common
				phone
				address
				coordinates {
					lat
					lng
				}
				administrativeArea
			}
		}
	}
`;
