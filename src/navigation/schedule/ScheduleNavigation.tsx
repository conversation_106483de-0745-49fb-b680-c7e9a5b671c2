import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React from 'react';
import Schedule from '../../screens/schedule/Schedule';
import { Screens } from '../../utils/enums';

const ScheduleNavigation = () => {
    const Stack = createNativeStackNavigator();

    return (
        <Stack.Navigator
            initialRouteName={Screens.CLIENT_SCHEDULE}
        >
            <Stack.Screen
                name={Screens.CLIENT_SCHEDULE}
                component={Schedule}
                options={{
                    headerBackVisible: false,
                    headerShown: false,
                }}
            />

            <Stack.Screen
                name={Screens.SERVICE_SCHEDULE}
                component={() => <></>}
                options={{
                    headerBackVisible: false,
                }}
            />

        </Stack.Navigator>
    );
};

export default ScheduleNavigation;
