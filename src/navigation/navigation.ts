import {
  CommonActions,
  createNavigationContainerRef,
} from '@react-navigation/native';
import {BackHandler} from 'react-native';

export const navigation = createNavigationContainerRef();

export const navigate = (
  screenName: string,
  params: any = {},
  resetStack: boolean | undefined = false,
) => {
  if (resetStack) {
    return navigation.dispatch(
      CommonActions.reset({
        routes: [
          {
            name: screenName,
            params,
          },
        ],
      }),
    );
  }

  return navigation.dispatch(
    CommonActions.navigate({name: screenName, params}),
  );
};

export const canGoBack = () => navigation.canGoBack();

const handleHardwareBack = () => !canGoBack();

export const applyBackHandleListener = () =>
  BackHandler.addEventListener('hardwareBackPress', handleHardwareBack);

export const removeBackHandleListener = () =>
  BackHandler.removeEventListener('hardwareBackPress', handleHardwareBack);
