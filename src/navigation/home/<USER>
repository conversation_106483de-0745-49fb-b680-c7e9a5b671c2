import {createNativeStackNavigator} from '@react-navigation/native-stack';
import React from 'react';
import Home from '../../screens/home/<USER>';
import {Screens} from '../../utils/enums';
import CarServices from './../../screens/carServices/CarServices';

const HomeNavigation = () => {
  const Stack = createNativeStackNavigator();

  return (
    <Stack.Navigator initialRouteName={Screens.HOME}>
      <Stack.Screen
        name={Screens.HOME}
        component={Home}
        options={{
          headerShown: false,
          headerBackVisible: false,
        }}
      />

      <Stack.Screen
        name={Screens.CAR_SERVICES}
        component={CarServices}
        options={{
          headerShown: false,
          headerBackVisible: false,
        }}
      />

      <Stack.Screen
        name={Screens.CREATE_APPOINTMENT}
        component={() => <></>}
        options={{
          headerShown: false,
          headerBackVisible: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default HomeNavigation;
