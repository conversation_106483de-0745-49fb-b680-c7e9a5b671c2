import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { TabNames } from '../../utils/enums';
import HomeNavigation from '../home/<USER>';
import ScheduleNavigation from '../schedule/ScheduleNavigation';
import ProfileNavigation from '../profile/ProfileNavigation';
import CustomTabBar from '../../components/customTabBar/CustomTabBar';

const Tab = createBottomTabNavigator();

const Tabs = () => {
	return (
		<SafeAreaView style={{ backgroundColor: '#8ED2FF', flex: 1, justifyContent: 'center', padding: 0, margin: 0 }} edges={['right', 'top', 'left']}>
			<Tab.Navigator tabBar={(props) => <CustomTabBar {...props} />} screenOptions={{ headerShown: false }} initialRouteName={TabNames.HOME}>
				<Tab.Screen name={TabNames.HOME} component={HomeNavigation} />
				<Tab.Screen name={TabNames.SCHEDULE} component={ScheduleNavigation} />
				<Tab.Screen name={TabNames.PROFILE} component={ProfileNavigation} />
			</Tab.Navigator>
		</SafeAreaView>
	);
};

export default Tabs;
