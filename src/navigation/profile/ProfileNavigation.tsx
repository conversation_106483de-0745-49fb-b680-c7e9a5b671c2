import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React from 'react';
import Profile from '../../screens/profile/Profile';
import { Screens } from '../../utils/enums';
import Settings from '../../screens/settings/Settings';
import ProfileDetails from '../../screens/profileDetails/ProfileDetails';

const ProfileNavigation = () => {
	const Stack = createNativeStackNavigator();

	return (
		<Stack.Navigator initialRouteName={Screens.PROFILE}>
			<Stack.Screen
				name={Screens.PROFILE}
				component={Profile}
				options={{
					headerShown: false
				}}
			/>
			<Stack.Screen
				name={Screens.SETTINGS}
				component={Settings}
				options={{
					headerShown: false
				}}
			/>
			<Stack.Screen
				name={Screens.PROFILE_DETAILS}
				component={ProfileDetails}
				options={{
					headerShown: false
				}}
			/>
		</Stack.Navigator>
	);
};

export default ProfileNavigation;
