import {View} from 'tamagui';
import AddVehicleWidget from '../../components/addVehicleWidget/AddVehicleWidget';
import UserHeader from '../../components/homeHeader/HomeHeader';

import ProfileWidget from '../../components/profileWidget/ProfileWidget';
import {ProfileWidgets} from '../../utils/enums';
import {useAuth} from '../../providers/AuthProvider';

const Profile = () => {
  const {logout} = useAuth();

  return (
    <View display={'flex'} backgroundColor={'#8ED2FF'} flex={1}>
      <UserHeader />
      <View
        display={'flex'}
        backgroundColor={'#ffffff'}
        flex={1}
        borderTopLeftRadius={20}
        borderTopRightRadius={20}
        padding={30}>
        <AddVehicleWidget />

        <View display={'flex'} flexDirection={'column'} marginTop={40} gap={20}>
          {ProfileWidgets.map((widget, index) => (
            <ProfileWidget key={index} {...widget} />
          ))}
          <ProfileWidget name="Deconectare" onPress={logout} />
        </View>
      </View>
    </View>
  );
};

export default Profile;
