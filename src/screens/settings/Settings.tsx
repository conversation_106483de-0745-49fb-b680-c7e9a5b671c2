import { StyleSheet, TouchableOpacity } from 'react-native';
import React from 'react';
import { canGoBack, navigate, navigation } from '../../navigation/navigation';
import Icon from 'react-native-vector-icons/FontAwesome6';
import { H1, H2, H3, Text, View, XStack } from 'tamagui';
import { Screens, UserType } from '../../utils/enums';
import { c } from '../../utils/styleVariables';
import { useUserInfo } from '../../providers/UserInfoProvider';

const Settings = () => {
	const { userInfo } = useUserInfo();
	return (
		<View paddingHorizontal={20} paddingVertical={10}>
			{canGoBack() && (
				<TouchableOpacity onPress={() => navigation.goBack()}>
					<Icon name="chevron-left" color={'black'} size={22} />
				</TouchableOpacity>
			)}
			<H1 marginVertical={20}>Set<PERSON>ri</H1>

			{userInfo?.userType === UserType.COMPANY && (
				<>
					<H3 marginVertical={20} textDecorationLine="underline">
						Abonament
					</H3>
					<XStack>
						<TouchableOpacity onPress={() => navigate(Screens.SUBSCRIBE, { canGoBack: true, isOnboardingFlow: false })}>
							<XStack
								width={'100%'}
								justifyContent="space-between"
								backgroundColor={c.brandBlueLowOpacity}
								padding={10}
								borderRadius={14}
								borderWidth={1}
								borderColor={c.brandBlue}
							>
								<Text>Schimbă/anulează abonamentul</Text>
								<Icon name="chevron-right" color={'black'} size={18} />
							</XStack>
						</TouchableOpacity>
					</XStack>
				</>
			)}
		</View>
	);
};

export default Settings;

const styles = StyleSheet.create({});
