import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React from 'react';
import { StatusBar, StyleSheet } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Login from './login/Login';
import { useAuth } from '../providers/AuthProvider';
import Tabs from '../navigation/tabs/Tabs';
import ConfirmSignup from './signup/ConfirmSignup';
import Signup from './signup/Signup';
import OnboardingStepOne from '../components/onboardingStepOne/OnboardingStepOne';
import OnboardingStepTwo from '../components/onboardingStepTwo/OnboardingStepTwo';
import OnboardingStepThree from '../components/onboardingStepThree/Subscribe';
import { useUserInfo } from '../providers/UserInfoProvider';
import OnboardingStepFour from '../components/onboardingStepFour/OnboardingStepFour';
import { UserType } from '../utils/enums';
import Subscribe from '../components/onboardingStepThree/Subscribe';

const Screens = () => {
	const Stack = createNativeStackNavigator();

	const styles = StyleSheet.create({
		backgroundStyle: {
			backgroundColor: '#8ED2FF',
			flex: 1,
			justifyContent: 'center'
		}
	});
	const { user } = useAuth();
	console.log('user', user);
	const { userInfo, userServices } = useUserInfo();
	console.log('userInfo', userInfo);
	return (
		<SafeAreaProvider>
			<StatusBar barStyle={'light-content'} backgroundColor={styles.backgroundStyle.backgroundColor} />

			<Stack.Navigator initialRouteName="Login" screenOptions={{ headerShown: false }}>
				{!user ? (
					<>
						<Stack.Screen name="Login" component={Login} />
						<Stack.Screen name="Signup" component={Signup} />
						<Stack.Screen name="ConfirmSignup" component={ConfirmSignup} />
					</>
				) : (
					<>
						{!userInfo?.userType && <Stack.Screen name="OnboardingStepOne" component={OnboardingStepOne} />}
						{!userInfo?.phone && <Stack.Screen name="OnboardingStepTwo" component={OnboardingStepTwo} />}
						{userInfo?.userType === UserType.COMPANY && !userInfo?.subscriptionType && (
							<Stack.Screen name="OnboardingStepThree" component={OnboardingStepThree} initialParams={{ canGoBack: false }} />
						)}
						{!userServices?.length && userInfo?.userType === UserType.COMPANY && (
							<Stack.Screen name="OnboardingStepFour" component={OnboardingStepFour} />
						)}

						<Stack.Screen name={'Tabs'} component={Tabs} />
						<Stack.Screen name={'Subscribe'} component={Subscribe} />
					</>
				)}
			</Stack.Navigator>
		</SafeAreaProvider>
	);
};

export default Screens;
