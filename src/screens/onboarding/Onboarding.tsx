// import React from 'react';
// import {useOnboarding} from '../../providers/OnboardingProvider';
// import OnboardingStepOne from '../../components/onboardingStepOne/OnboardingStepOne';
// import OnboardingStepTwo from '../../components/onboardingStepTwo/OnboardingStepTwo';
// import OnboardingStepThree from '../../components/onboardingStepThree/OnboardingStepThree';

// const Onboarding = () => {
//   const {onboardingActiveStep} = useOnboarding();
//   switch (onboardingActiveStep) {
//     case 1:
//       return <OnboardingStepOne />;
//     case 2:
//       return <OnboardingStepTwo />;
//     case 3:
//       return <OnboardingStepThree />;
//   }
// };

// export default Onboarding;
