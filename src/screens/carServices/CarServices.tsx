import { useRoute } from '@react-navigation/native';
import { useEffect, useState } from 'react';
import { ScrollView, Text, View } from 'tamagui';
import { ServiceWidgetParams } from '../../common/interfaces';
import ServiceWidget from '../../components/serviceWidget/ServiceWidget';
import { CarShopServices } from '../../utils/enums';

const CarServices = () => {
	const route = useRoute();

	const [carServices, setCarServices] = useState(CarShopServices);

	const { serviceId = 0 } = route?.params as { serviceId: number };

	const [pickedService, setPickedService] = useState(serviceId);

	const handleServicePick = (serviceId: number) => {
		const serviceIndex = [...carServices].findIndex((service) => service.id === serviceId);

		setCarServices((previousValue) => {
			previousValue.unshift(previousValue.splice(serviceIndex, 1)[0]);
			return [...previousValue];
		});
	};

	useEffect(() => {
		if (pickedService !== 0) {
			handleServicePick(pickedService);
		}
	}, [pickedService]);

	// temp
	const services: ServiceWidgetParams[] = [
		{
			name: 'EurTech Auto',
			address: 'Bucuresti, Grozavesti',
			location: { lat: '123', long: '123' }
		}
	];

	return (
		<View display={'flex'} backgroundColor={'#ffffff'} flex={1} padding={30}>
			<Text fontSize={21}>Alege serviciul pe care îl cauți</Text>

			<ScrollView display={'flex'} marginVertical={20} horizontal={true} maxHeight={35} marginBottom={40}>
				{carServices.map((service, index) => (
					<View
						key={index}
						display={'flex'}
						flexDirection={'row'}
						justifyContent={'center'}
						alignItems={'center'}
						borderWidth={1}
						borderStyle={'solid'}
						borderColor={pickedService === service.id ? '#8ED2FF' : '#979797'}
						backgroundColor={pickedService === service.id ? '#8ed2ff3f' : '#ffffff'}
						borderRadius={10}
						padding={7}
						height={35}
						marginRight={12}
						onPress={() => setPickedService(service.id)}
					>
						<Text fontSize={15} fontWeight={600}>
							{service.name}
						</Text>
					</View>
				))}
			</ScrollView>
			<View>
				{services.map((service) => (
					<ServiceWidget {...service} />
				))}
			</View>
		</View>
	);
};

export default CarServices;
