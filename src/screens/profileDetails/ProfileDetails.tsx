import { StyleSheet } from 'react-native';
import React, { useState } from 'react';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { UserType } from '../../utils/enums';
import { Formik } from 'formik';
import FormWrapper from '../../components/formWrapper/FormWrapper';
import { Button, Text, View, YStack } from 'tamagui';
import OnboardingClientInputs from '../../components/onboardingClientInputs/OnboardingClientInputs';
import OnboardingCompanyInputs from '../../components/onboardingCompanyInputs/OnboardingCompanyInputs';
import { clientDetailsSchema, companyDetailsSchema } from '../../utils/schemaValidation';
import { c } from '../../utils/styleVariables';
import { useMutation } from '@apollo/client';
import { UPDATE_USER_INFO } from '../../queries/UPDATE_USER_INFO';

const ProfileDetails = () => {
	const { userInfo } = useUserInfo();
	const [updateUser] = useMutation(UPDATE_USER_INFO, {
		fetchPolicy: 'network-only'
	});
	const mappedUserInfoFields = {
		lastName: 'lastName',
		firstName: 'firstName',
		phone: 'phone',
		address: 'address',
		companyPhone: 'phone',
		companyAddress: 'address',
		companyName: 'companyName',
		lastNameRepresentative: 'representativeLastName',
		firstNameRepresentative: 'representativeFirstName',
		lat: 'coordinates.lat',
		lng: 'coordinates.lng',
		administrativeArea: 'administrativeArea',
		companyPostalCode: 'postalCode',
		taxId: 'taxId'
	};
	const getDifferentFields = (obj1, obj2) => {
		console.log('obj1', obj1);
		console.log('obj2', obj2);

		const getNestedValue = (obj, path) => {
			return path.split('.').reduce((current, key) => current?.[key], obj);
		};

		return Object.keys(obj1).filter((key) => {
			const mappedKey = mappedUserInfoFields[key];
			if (!mappedKey) return false;

			const obj2Value = getNestedValue(obj2, mappedKey);
			const obj1Value = obj1[key];

			// Compare values, handling null/undefined cases
			// If both are null/undefined, they're the same
			if (obj1Value == null && obj2Value == null) return false;
			// If one is null/undefined and the other isn't, they're different
			if ((obj1Value == null) !== (obj2Value == null)) return true;
			// Convert both values to strings for comparison (since coordinates might be strings vs numbers)
			return String(obj1Value) !== String(obj2Value);
		});
	};
	const handleFormSubmit = (values) => {
		const changedFields = getDifferentFields(values, userInfo);
		console.log('changed fields', changedFields);

		updateUser({
			variables: {
				user: {
					userId: user?.id,
					userType,
					companyName: values.companyName,
					representativeFirstName: values.firstNameRepresentative,
					representativeLastName: values.lastNameRepresentative,
					phone: values.companyPhone,
					address: values.companyAddress,
					coordinates: {
						lat: values.lat.toString(),
						lng: values.lng.toString()
					},
					administrativeArea: values.administrativeArea,
					country: userSubscriptionDetails.country,
					city: userSubscriptionDetails.city,
					street: userSubscriptionDetails.street,
					streetNumber: userSubscriptionDetails.streetNumber,
					postalCode: userSubscriptionDetails.postalCode,
					state: userSubscriptionDetails.state,
					taxId: values?.taxId
				}
			},
			onCompleted: ({ updateUser: updateUserData }) => {
				if (updateUserData.status !== 500) {
					console.log('User updated successfully');
					navigate(Screens.ONBOARDING_STEP_THREE);
				} else {
					console.log('Failed to update user');
				}
			},
			onError: (e: any) => {
				console.log('Error updating user', e);
			}
		});
	};
	const [isEditing, setIsEditing] = useState(false);

	const companyMappedDetails = [
		{
			name: 'Denumire companie',
			value: 'companyName'
		},
		{
			name: 'Telefon',
			value: 'phone'
		},
		{
			name: 'Nume reprezentant',
			value: 'representativeLastName'
		},
		{
			name: 'Prenume reprezentant',
			value: 'representativeFirstName'
		},
		{
			name: 'Adresă curentă',
			value: 'address'
		},
		{
			name: 'Cod poștal',
			value: 'postalCode'
		},
		{
			name: 'CUI',
			value: 'taxId'
		}
	];
	const clientMappedDetails = [
		{
			name: 'Prenume',
			value: 'firstName'
		},
		{
			name: 'Nume',
			value: 'lastName'
		},
		{
			name: 'Telefon',
			value: 'phone'
		},
		{
			name: 'Adresă curentă',
			value: 'address'
		}
	];
	const handleEditPress = () => {
		setIsEditing(!isEditing);
	};
	return (
		<View flex={1}>
			<FormWrapper title="Detalii utilizator">
				{!isEditing && (
					<Button marginVertical="$2" onPress={handleEditPress}>
						Editează
					</Button>
				)}
				{isEditing ? (
					<Formik
						validationSchema={userInfo?.userType === UserType.CLIENT ? clientDetailsSchema : companyDetailsSchema}
						initialValues={{
							lastName: userInfo?.lastName,
							firstName: userInfo?.firstName,
							phone: userInfo?.phone,
							address: userInfo?.address,
							companyPhone: userInfo?.phone,
							companyAddress: userInfo?.address,
							companyName: userInfo?.companyName,
							lastNameRepresentative: userInfo?.representativeLastName,
							firstNameRepresentative: userInfo?.representativeFirstName,
							lat: userInfo?.coordinates?.lat,
							lng: userInfo?.coordinates?.lng,
							administrativeArea: userInfo?.administrativeArea,
							companyPostalCode: userInfo?.postalCode,
							taxIdCollection: false,
							taxId: userInfo?.taxId
						}}
						onSubmit={(values) => handleFormSubmit(values)}
					>
						{({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
							<YStack
								flex={1}
								backgroundColor={c.white}
								alignItems="center"
								gap="$4"
								paddingVertical="$4"
								paddingHorizontal="$4"
								borderRadius="$6"
							>
								{userInfo?.userType === UserType.CLIENT ? (
									<OnboardingClientInputs
										handleChange={handleChange}
										handleBlur={handleBlur}
										values={values}
										errors={errors}
										touched={touched}
									/>
								) : (
									<OnboardingCompanyInputs
										isOnboarding={false}
										handleChange={handleChange}
										handleBlur={handleBlur}
										values={values}
										errors={errors}
										touched={touched}
									/>
								)}

								<Button
									onPress={handleSubmit}
									width={'100%'}
									backgroundColor={c.brandBlue}
									color={c.white}
									marginTop="$5"
									alignSelf="center"
									size="$5"
								>
									Salvează
								</Button>
								<Button
									onPress={handleEditPress}
									width={'100%'}
									backgroundColor={c.cancelRed}
									color={c.white}
									marginTop="$1"
									alignSelf="center"
									size="$5"
								>
									Renunță
								</Button>
							</YStack>
						)}
					</Formik>
				) : userInfo?.userType === UserType.COMPANY ? (
					<YStack flex={1} backgroundColor={c.white} alignItems="flex-start" gap="$4" paddingVertical="$4" paddingHorizontal="$4" borderRadius="$6">
						{companyMappedDetails.map((field) => (
							<YStack>
								<Text fontWeight={600}>{field.name}</Text>
								<Text>{userInfo?.[field.value]}</Text>
							</YStack>
						))}
					</YStack>
				) : (
					<YStack flex={1} backgroundColor={c.white} alignItems="flex-start" gap="$4" paddingVertical="$4" paddingHorizontal="$4" borderRadius="$6">
						{clientMappedDetails.map((field) => (
							<YStack>
								<Text fontWeight={600}>{field.name}</Text>
								<Text>{userInfo?.[field.value]}</Text>
							</YStack>
						))}
					</YStack>
				)}
			</FormWrapper>
		</View>
	);
};

export default ProfileDetails;

const styles = StyleSheet.create({});
