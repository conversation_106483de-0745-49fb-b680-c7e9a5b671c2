import { Text, View } from "tamagui";
import ServiceWidget from "../../components/serviceWidget/ServiceWidget";

const Schedule = () => {

    // temp
    const appointments = [{
        name: 'EurTech Auto', address: 'Bucuresti, Grozavesti', location: { lat: '123', long: '123' }
    }];

    return (
        <View
            display={'flex'}
            backgroundColor={'#ffffff'}
            flex={1}
            padding={30}
        >
            <Text
                fontSize={21}
                marginBottom={40}
            >
                📅 Programările tale
            </Text>
            <View>
                {appointments.map((appointment, index) => (
                    <ServiceWidget key={index} {...appointment} />
                ))}
            </View>
        </View>
    )
}

export default Schedule;