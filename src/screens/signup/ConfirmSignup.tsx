import {Platform, StyleSheet, Text, View} from 'react-native';
import React, {useState} from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';

import FormWrapper from '../../components/formWrapper/FormWrapper';
import {YStack} from 'tamagui';
import {c} from '../../utils/styleVariables';
import LottieView from 'lottie-react-native';
import {useAuth} from '../../providers/AuthProvider';
import {useRoute} from '@react-navigation/native';
import {ConfirmSignupParams} from '../../common/interfaces';

const CELL_SIZE = 40;
const CELL_BORDER_RADIUS = 14;
const CELL_COUNT = 6;

const ConfirmSignup = () => {
  const [value, setValue] = useState('');
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });

  const [errorMessage, setErrorMessage] = useState<string | undefined>('');

  const route = useRoute();
  const {username} = route?.params as ConfirmSignupParams;

  const {handleSignUpConfirmation} = useAuth();
  const ref = useBlurOnFulfill({value, cellCount: CELL_COUNT});
  const handleChangeText = async (newValue: string) => {
    setValue(newValue);
    setErrorMessage('');

    if (newValue.length === CELL_COUNT) {
      const response = await handleSignUpConfirmation({
        username,
        confirmationCode: newValue,
      });

      if (response.hasError) {
        setErrorMessage(response.errorMessage);
      }
    }
  };
  return (
    <FormWrapper
      titleProps={{alignSelf: 'center'}}
      subtitleProps={{alignSelf: 'center'}}
      HeaderComponent={
        <LottieView
          source={require('../../assets/lottie/verifyEmail.lottie')}
          autoPlay
          loop={false}
          style={{
            width: 200,
            height: 200,
            alignSelf: 'center',
            marginVertical: 30,
          }}
        />
      }
      title="Cod de verificare"
      subtitle="Introdu codul de verificare primit pe email."
      goBack={true}>
      <YStack
        alignItems="center"
        gap="$2"
        paddingVertical="$4"
        paddingHorizontal="$2"
        borderRadius="$6">
        <CodeField
          ref={ref}
          {...props}
          value={value}
          onChangeText={handleChangeText}
          cellCount={CELL_COUNT}
          rootStyle={styles.codeFieldRoot}
          keyboardType="number-pad"
          textContentType="oneTimeCode"
          autoComplete={Platform.select({
            android: 'sms-otp',
            default: 'one-time-code',
          })}
          testID="my-code-input"
          renderCell={({index, symbol, isFocused}) => (
            <Text
              key={index}
              style={[styles.cell, isFocused && styles.focusCell]}
              onLayout={getCellOnLayoutHandler(index)}>
              {symbol || (isFocused ? <Cursor /> : null)}
            </Text>
          )}
        />

        {errorMessage && (
          <View
            style={{backgroundColor: c.white, borderRadius: 10, padding: 10}}>
            <Text style={{color: c.cancelRed}}>{errorMessage}</Text>
          </View>
        )}
      </YStack>
    </FormWrapper>
  );
};

export default ConfirmSignup;

const styles = StyleSheet.create({
  root: {flex: 1, padding: 20},
  title: {textAlign: 'center', fontSize: 30},
  codeFieldRoot: {
    marginVertical: 20,
    width: '100%',
  },
  cell: {
    //iOS
    overflow: 'hidden',
    // Android
    elevation: 2,
    backgroundColor: c.white,
    borderRadius: CELL_BORDER_RADIUS,
    height: CELL_SIZE,
    width: CELL_SIZE,
    lineHeight: CELL_SIZE - 5,
    ...Platform.select({web: {lineHeight: 65}}),
    fontSize: 30,
    borderWidth: 2,
    borderColor: c.lighterGray,
    textAlign: 'center',
    color: c.black,
    borderStyle: 'solid',
  },
  focusCell: {
    borderColor: c.black,
  },
});
