import {TouchableWithoutFeedback} from 'react-native';
import React, {useState} from 'react';
import {<PERSON><PERSON>, Separator, Spinner, Text, XStack, YStack} from 'tamagui';
import {Formik} from 'formik';

import {useAuth} from '../../providers/AuthProvider';
import {c} from '../../utils/styleVariables';
import CustomFormInput from '../../components/customFormInput/CustomFormInput';
import {SignUpForm} from '../../common/interfaces';
import {registrationSchema} from '../../utils/schemaValidation';
import FormWrapper from '../../components/formWrapper/FormWrapper';
import SocialLoginButtons from '../../components/socialLoginButtons/SocialLoginButtons';
import {navigate} from '../../navigation/navigation';
import {Screens} from '../../utils/enums';

const Signup = () => {
  const {signUpWithEmailPassword} = useAuth();
  const [status, setStatus] = useState<'off' | 'submitting' | 'submitted'>(
    'off',
  );
  const [confirmPasswordError, setConfirmPasswordError] = useState('');

  const handleFormSubmit = async (values: SignUpForm) => {
    setStatus('submitting');

    if (values.password !== values.confirmPassword) {
      setConfirmPasswordError('Parolele nu coincid');
      return;
    } else {
      setConfirmPasswordError('');
    }

    const response = await signUpWithEmailPassword({
      email: values.email,
      password: values.password,
    });
    if (response.isSignUpComplete || response.nextStep || response.hasError) {
      setStatus('submitted');
    }
    if (response.nextStep?.signUpStep === 'CONFIRM_SIGN_UP') {
      navigate(Screens.CONFIRM_SIGNUP, {username: values.email});
    }
    if (response.errorMessage) {
      setConfirmPasswordError(response.errorMessage);
    }
  };

  return (
    <FormWrapper
      title="Înregistrează-te"
      subtitle={
        <>
          Ai deja un cont?{' '}
          <TouchableWithoutFeedback
            onPress={() => navigate(Screens.LOGIN, {}, true)}>
            <Text color={'white'} textDecorationLine="underline">
              Intră în cont
            </Text>
          </TouchableWithoutFeedback>
        </>
      }>
      <Formik
        validationSchema={registrationSchema}
        initialValues={{email: '', password: '', confirmPassword: ''}}
        onSubmit={(values, actions) => handleFormSubmit(values)}>
        {({
          handleChange,
          handleBlur,
          handleSubmit,
          values,
          errors,
          touched,
        }) => (
          <YStack
            flex={1}
            backgroundColor={c.white}
            alignItems="center"
            gap="$2"
            paddingVertical="$4"
            paddingHorizontal="$4"
            borderRadius="$6">
            <CustomFormInput
              labelText={'Email'}
              labelProps={{htmlFor: 'email', width: '100%'}}
              inputProps={{
                width: '100%',
                id: 'email',
                size: '$4',
                borderWidth: 2,
                placeholder: 'Email',
                textContentType: 'emailAddress',
              }}
              errorText={errors.email && touched.email ? errors.email : null}
              onChangeText={handleChange('email')}
              onBlur={handleBlur('email')}
              value={values.email}
            />
            <CustomFormInput
              labelText={'Parolă'}
              labelProps={{htmlFor: 'password', width: '100%'}}
              inputProps={{
                width: '100%',
                id: 'password',
                size: '$4',
                borderWidth: 2,
                placeholder: 'Parolă',
                textContentType: 'newPassword',
                secureTextEntry: true,
              }}
              errorText={
                errors.password && touched.password ? errors.password : null
              }
              onChangeText={handleChange('password')}
              onBlur={handleBlur('password')}
              value={values.password}
            />

            <CustomFormInput
              labelText={'Confirmă Parola'}
              labelProps={{htmlFor: 'confirmPassword', width: '100%'}}
              inputProps={{
                width: '100%',
                id: 'confirmPassword',
                size: '$4',
                borderWidth: 2,
                placeholder: 'Confirmă Parola',
                textContentType: 'newPassword',
                secureTextEntry: true,
              }}
              errorText={
                errors.confirmPassword && touched.confirmPassword
                  ? errors.confirmPassword
                  : confirmPasswordError
              }
              onChangeText={handleChange('confirmPassword')}
              onBlur={handleBlur('confirmPassword')}
              value={values.confirmPassword}
            />

            <Button
              onPress={handleSubmit}
              width={'100%'}
              backgroundColor={c.brandBlue}
              color={c.white}
              marginTop="$5"
              alignSelf="center"
              size="$5"
              icon={status === 'submitting' ? () => <Spinner /> : undefined}>
              Înregistrează-te
            </Button>

            <XStack alignItems="center" justifyContent="center" gap={'$2'}>
              <Separator marginVertical={15} backgroundColor={c.black} />
              <Text alignSelf="center" marginVertical="$5">
                Sau înregistrează-te folosind
              </Text>
              <Separator marginVertical={15} backgroundColor={c.black} />
            </XStack>
            <SocialLoginButtons />
          </YStack>
        )}
      </Formik>
    </FormWrapper>
  );
};

export default Signup;
