import {TouchableWithoutFeedback} from 'react-native';
import React, {useState} from 'react';
import {<PERSON><PERSON>, Separator, Spinner, Text, XStack, YStack} from 'tamagui';
import {Formik} from 'formik';

import {useAuth} from '../../providers/AuthProvider';
import {c} from '../../utils/styleVariables';
import CustomFormInput from '../../components/customFormInput/CustomFormInput';
import {SignInForm} from '../../common/interfaces';
import {loginSchema} from '../../utils/schemaValidation';
import FormWrapper from '../../components/formWrapper/FormWrapper';
import SocialLoginButtons from '../../components/socialLoginButtons/SocialLoginButtons';
import {navigate} from '../../navigation/navigation';
import {Screens} from '../../utils/enums';

const Login = () => {
  const {handleSignIn} = useAuth();
  const [status, setStatus] = useState<'off' | 'submitting' | 'submitted'>(
    'off',
  );
  const [errorMessage, setErrorMessage] = useState<string | undefined>();
  const handleFormSubmit = async (values: SignInForm) => {
    setStatus('submitting');
    setErrorMessage('');
    const response = await handleSignIn({
      username: values.email,
      password: values.password,
    });
    if (response.isSignedIn || response.nextStep || response.hasError) {
      setStatus('submitted');
    }
    if (response?.nextStep?.signInStep === 'CONFIRM_SIGN_UP') {
      navigate(Screens.CONFIRM_SIGNUP, {username: values.email});
    }

    if (response.errorMessage) {
      setErrorMessage(response.errorMessage);
    }
  };

  return (
    <FormWrapper
      title="Intră în cont"
      subtitle={
        <>
          Nu ai un cont?{' '}
          <TouchableWithoutFeedback
            onPress={() => navigate(Screens.SIGNUP, {}, true)}>
            <Text color={'white'} textDecorationLine="underline">
              Creează-ți un cont nou!
            </Text>
          </TouchableWithoutFeedback>
        </>
      }>
      <Formik
        validationSchema={loginSchema}
        initialValues={{email: '', password: ''}}
        onSubmit={(values, actions) => handleFormSubmit(values)}>
        {({
          handleChange,
          handleBlur,
          handleSubmit,
          values,
          errors,
          touched,
        }) => (
          <YStack
            flex={1}
            backgroundColor={c.white}
            alignItems="center"
            gap="$2"
            paddingVertical="$4"
            paddingHorizontal="$4"
            borderRadius="$6">
            <CustomFormInput
              labelText={'Email'}
              labelProps={{htmlFor: 'email-login', width: '100%'}}
              inputProps={{
                width: '100%',
                id: 'email-login',
                size: '$4',
                borderWidth: 2,
                placeholder: 'Email',
                textContentType: 'emailAddress',
              }}
              errorText={errors.email && touched.email ? errors.email : null}
              onChangeText={handleChange('email')}
              onBlur={handleBlur('email')}
              value={values.email}
            />
            <CustomFormInput
              labelText={'Parolă'}
              labelProps={{htmlFor: 'password-login', width: '100%'}}
              inputProps={{
                width: '100%',
                id: 'password-login',
                size: '$4',
                borderWidth: 2,
                placeholder: 'Parolă',
                textContentType: 'newPassword',
                secureTextEntry: true,
              }}
              errorText={
                errors.password && touched.password
                  ? errors.password
                  : errorMessage
              }
              onChangeText={handleChange('password')}
              onBlur={handleBlur('password')}
              value={values.password}
            />

            <Button
              onPress={handleSubmit}
              width={'100%'}
              backgroundColor={c.brandBlue}
              color={c.white}
              marginTop="$5"
              alignSelf="center"
              size="$5"
              icon={status === 'submitting' ? () => <Spinner /> : undefined}>
              Intră în cont
            </Button>

            <XStack alignItems="center" justifyContent="center" gap={'$2'}>
              <Separator marginVertical={15} backgroundColor={c.black} />
              <Text alignSelf="center" marginVertical="$5">
                Sau folosește
              </Text>
              <Separator marginVertical={15} backgroundColor={c.black} />
            </XStack>
            <SocialLoginButtons />
          </YStack>
        )}
      </Formik>
    </FormWrapper>
  );
};

export default Login;
