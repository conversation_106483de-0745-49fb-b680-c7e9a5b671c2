import * as yup from 'yup';

const phoneRegEx = /^(?:\+40|0)[1-9][0-9]{8}$/;

export const registrationSchema = yup.object().shape({
	email: yup.string().email('Adresă de email invalidă').required('Adresă de email lipseste'),
	password: yup.string().required('<PERSON><PERSON><PERSON> lipse<PERSON>te'),
	confirmPassword: yup.string().required('Confirmare Parola lipsește')
});

export const loginSchema = yup.object().shape({
	email: yup.string().email('Adresă de email invalidă').required('Adresă de email lipsește'),
	password: yup.string().required('Parola lipsește')
});

export const clientDetailsSchema = yup.object().shape({
	firstName: yup.string().required('Prenumele lipsește'),
	lastName: yup.string().required('Numele lipsește'),
	phone: yup.string().matches(phoneRegEx, '<PERSON>umăr de telefon invalid').required('Numărul de telefon lipsește'),
	address: yup.string().required('<PERSON><PERSON>a lipsește')
});

export const companyDetailsSchema = yup.object().shape({
	companyName: yup.string().required('Denumirea companiei lipsește'),
	firstNameRepresentative: yup.string().required('Prenume reprezentant lipsește'),
	lastNameRepresentative: yup.string().required('Numele reprezentant lipsește'),
	companyPhone: yup.string().matches(phoneRegEx, 'Număr de telefon invalid').required('Numărul de telefon lipsește'),
	companyAddress: yup.string().required('Adresa lipsește'),
	companyPostalCode: yup.string().required('Codul poștal lipsește'),
	taxIdCollection: yup.boolean().required(),
	taxId: yup.string().when('taxIdCollection', {
		is: true,
		then: (schema) => schema.min(6, 'Minim 6 cifre').max(9, 'Maxim 9 cifre').required('CUI lipsește'),
		otherwise: (schema) => schema.optional()
	})
});

export const requiredTaxId = {};

export const addServicesSchema = yup.object().shape({
	startTime: yup.string().required('Ora de start lipsește'),
	endTime: yup.string().required('Ora de sfârșit lipsește'),
	appointments: yup.number().required('Numărul de programări în paralel lipsește'),
	services: yup
		.array()
		.of(
			yup.object().shape({
				offeredService: yup.string().required('Tipul serviciului lipsește'),
				carMakes: yup
					.array()
					.of(
						yup.object().shape({
							name: yup.string().required('Marcă autoturism lipsește')
						})
					)
					.min(1, 'Trebuie să selectezi cel puțin o opțiune'),
				price: yup.number()
			})
		)
		.required('Trebuie să adaugi minim un serviciu')
		.min(1, 'Trebuie să adaugi minim un serviciu')
});
