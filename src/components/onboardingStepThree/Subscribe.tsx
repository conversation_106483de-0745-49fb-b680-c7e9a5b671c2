import { Dimensions, TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import Carousel, { ICarouselInstance } from 'react-native-reanimated-carousel';
import { H2, View } from 'tamagui';
import { SafeAreaView } from 'react-native-safe-area-context';
import SubscriptionOption from '../SubscriptionOption/SubscriptionOption';
import { navigate, navigation } from '../../navigation/navigation';
import { Screens, SubscriptionType, TabNames } from '../../utils/enums';
import { useMutation } from '@apollo/client';
import { SUBSCRIBE_USER } from '../../queries/SUBSCRIBE_USER';
import { useOnboarding } from '../../providers/OnboardingProvider';
import { PaymentSheetError, useStripe } from '@stripe/stripe-react-native';
import { useAuth } from '../../providers/AuthProvider';
import { useAppConfig } from '../../providers/AppProvider';
import { UPDATE_USER_INFO } from '../../queries/UPDATE_USER_INFO';
import Icon from 'react-native-vector-icons/FontAwesome6';
import { useRoute } from '@react-navigation/native';
import { useUserInfo } from '../../providers/UserInfoProvider';

const PAGE_WIDTH = Dimensions.get('window').width;

const Subscribe = () => {
	const carouselRef = React.useRef<ICarouselInstance>(null);
	const { featureFlags } = useAppConfig();
	const { userInfo, setUserProps } = useUserInfo();

	const benefitsListPro = [
		'Adaugă până la 10 servicii',
		'Programări nelimitate',
		'Programări de Luni până Duminică',
		'Suport prin email și telefon',
		'Acces la statistici detaliate (număr de programări, recenzii etc.)',
		'Programări în paralel'
	];
	const benefitsListBasic = ['Adaugă până la 3 servicii', 'Până la 2 programări pe zi', 'Programări de Luni până Vineri', 'Suport prin email'];
	const options = [
		{
			name: 'PRO',
			title: '⚡️ PRO',
			benefitsList: benefitsListPro,
			price: '149 RON/lună',
			isPro: true,
			selectionButtonTitle: 'Alege PRO',
			onPress: async () => {
				await createCustomerAndSubscription();
			},
			priceId: featureFlags.MONTHLY_SUBSCRIPTION_PRICE_ID
		},
		{
			name: 'Basic',
			title: '🍃 Basic',
			benefitsList: benefitsListBasic,
			price: 'Gratuit',
			isPro: false,
			selectionButtonTitle: 'Alege Basic',
			onPress: () => {
				updateUserSubscription();
			}
		}
	];
	const route = useRoute();
	const { canGoBack = false, isOnboardingFlow = true } = route?.params;

	const updateUserSubscription = async () => {
		updateUser({
			variables: {
				user: {
					userId: user?.id,
					subscriptionType: SubscriptionType.BASIC
				}
			},
			onCompleted: ({ updateUser: updateUserData }) => {
				if (updateUserData.status !== 500) {
					console.log('User subscription updated successfully');
					if (isOnboardingFlow) {
						setCompanyOnboardingInfo({ ...companyOnboardingInfo, subscriptionType: SubscriptionType.BASIC });
						navigate(Screens.ONBOARDING_STEP_FOUR);
					} else {
						setUserProps({ subscriptionType: SubscriptionType.BASIC });
						navigation.goBack();
					}
				} else {
					console.log('Failed to update user');
				}
			},
			onError: (e: any) => {
				console.log('Error updating user', e);
			}
		});
	};

	const [selectedOption, setSelectedOption] = useState(options[0]);

	const [subscribeUser] = useMutation(SUBSCRIBE_USER, {
		fetchPolicy: 'network-only'
	});
	const { user } = useAuth();
	const { companyOnboardingInfo, userSubscriptionDetails, setCompanyOnboardingInfo } = useOnboarding();
	const { initPaymentSheet, presentPaymentSheet } = useStripe();
	const initializePaymentSheet = async ({ clientSecret, amount, currencyCode }) => {
		const { error } = await initPaymentSheet({
			paymentIntentClientSecret: clientSecret,
			returnURL: 'easygarage://payment-sheet',
			// Set `allowsDelayedPaymentMethods` to true if your business handles
			// delayed notification payment methods like US bank accounts.
			allowsDelayedPaymentMethods: false,
			intentConfiguration: {
				mode: {
					amount,
					currencyCode
				},
				confirmHandler: () => {}
			},
			merchantDisplayName: 'Easy Garage'
		});
		if (error) {
			// Handle error
			console.log('Error initializing payment sheet', error);
		}
	};
	const [updateUser] = useMutation(UPDATE_USER_INFO, {
		fetchPolicy: 'network-only'
	});

	const createCustomerAndSubscription = async () => {
		const userDetails = isOnboardingFlow
			? {
					userId: user?.id,
					representativeFirstName: companyOnboardingInfo?.firstNameRepresentative,
					representativeLastName: companyOnboardingInfo?.lastNameRepresentative,
					companyName: companyOnboardingInfo?.companyName,
					...userSubscriptionDetails
			  }
			: {
					userId: userInfo?.id,
					representativeFirstName: userInfo?.representativeFirstName,
					representativeLastName: userInfo?.representativeLastName,
					companyName: userInfo?.companyName,
					country: userInfo?.country,
					city: userInfo?.city,
					street: userInfo?.street,
					streetNumber: userInfo?.streetNumber,
					postalCode: userInfo?.postalCode,
					state: userInfo?.state
			  };
		await subscribeUser({
			variables: {
				userDetails,
				priceId: selectedOption.priceId
			},
			onCompleted: async ({ subscribeUser: subscribeUserData }) => {
				if (subscribeUserData.status === 200) {
					await initializePaymentSheet({
						clientSecret: subscribeUserData.clientSecret,
						amount: subscribeUserData.amount,
						currencyCode: subscribeUserData.currencyCode
					});
					const { error } = await presentPaymentSheet();
					if (error) {
						if (error.code === PaymentSheetError.Failed) {
							// Handle failed
							console.log('failed error', error);
						} else if (error.code === PaymentSheetError.Canceled) {
							// Handle canceled
							console.log('canceled error', error);
						}
					} else {
						// Payment succeeded
						// update user subscription info

						updateUser({
							variables: {
								user: {
									userId: user?.id,
									subscriptionType: SubscriptionType.PRO
								}
							},
							onCompleted: ({ updateUser: updateUserData }) => {
								if (updateUserData.status !== 500) {
									console.log('User updated successfully');
									if (isOnboardingFlow) {
										setCompanyOnboardingInfo({ ...companyOnboardingInfo, subscriptionType: SubscriptionType.PRO });
										navigate(Screens.ONBOARDING_STEP_FOUR);
									} else {
										setUserProps({ subscriptionType: SubscriptionType.PRO });
										navigate(TabNames.HOME);
									}
								} else {
									console.log('Failed to update user');
								}
							},
							onError: (e: any) => {
								console.log('Error updating user', e);
							}
						});
					}
				} else {
					console.log('Error subscribing user: ', subscribeUserData.message);
				}
			},
			onError: (e: any) => {
				console.error('Error subscribing user', e);
			}
		});
	};

	const handleSnapItem = (index: number) => {
		setSelectedOption(options?.[index]);
	};

	return (
		<SafeAreaView>
			<View gap="50%" alignItems="center" flexDirection="row">
				{canGoBack && (
					<TouchableOpacity style={{ marginLeft: 20 }} onPress={() => navigation.goBack()}>
						<Icon name="chevron-left" color={'black'} size={22} />
					</TouchableOpacity>
				)}
				<H2 alignSelf="center">Planuri de abonament</H2>
			</View>
			<Carousel
				{...{
					vertical: false,
					width: PAGE_WIDTH
				}}
				containerStyle={{ paddingVertical: 20 }}
				loop
				enabled
				ref={carouselRef}
				testID={'xxx'}
				style={{ width: '100%', paddingBottom: 50 }}
				autoPlay={false}
				data={options}
				pagingEnabled={true}
				onSnapToItem={handleSnapItem}
				renderItem={({ item }) => <SubscriptionOption userActiveSubsType={userInfo?.subscriptionType} {...item} />}
			/>
		</SafeAreaView>
	);
};

export default Subscribe;
