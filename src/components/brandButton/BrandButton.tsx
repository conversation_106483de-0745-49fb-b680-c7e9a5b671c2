import React from 'react';
import {Button} from 'tamagui';
import {c} from '../../utils/styleVariables';

const BrandButton = props => {
  return (
    <Button
      onPress={props.onPress}
      alignSelf="center"
      size={props?.size || '$4'}
      shadowColor={c.brandBlue}
      shadowOffset={{
        height: 0,
        width: 0,
      }}
      color={c.brandBlue}
      fontWeight={'bold'}
      shadowOpacity={0.4}
      shadowRadius={8}
      elevation={8}
      backgroundColor={c.white}>
      {props.title}
    </Button>
  );
};

export default BrandButton;
