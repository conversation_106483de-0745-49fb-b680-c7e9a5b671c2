import { Image, Text, View } from "tamagui";
import { navigate } from "../../navigation/navigation";
import { CarShopServices, Screens } from "../../utils/enums";
import AddVehicleWidget from "../addVehicleWidget/AddVehicleWidget";

const ClientHome = () => {

    return (
        <View
            display={'flex'}
            backgroundColor={'#ffffff'}
            flex={1}
            borderTopLeftRadius={20}
            borderTopRightRadius={20}
            padding={30}
        >
            <Text
                fontSize={23}
            >
                Alege serviciul pe care îl cauți
            </Text>
            <View
                display={'flex'}
                flexDirection={'row'}
                flexWrap={'wrap'}
                gap={16}
                marginVertical={20}

            >
                {CarShopServices.map((service, index) => (
                    <View
                        key={index}
                        display={'flex'}
                        flexDirection={'row'}
                        justifyContent={'center'}
                        alignItems={'center'}
                        borderWidth={1}
                        borderStyle={'solid'}
                        borderColor={'#979797'}
                        borderRadius={10}
                        padding={7}
                        onPress={() => navigate(Screens.CAR_SERVICES, { serviceId: service.id })}
                    >
                        <Text
                            fontSize={15}
                            fontWeight={600}
                        >{service.name}</Text>
                    </View>
                ))}
            </View>
            <AddVehicleWidget />
        </View>
    )
}

export default ClientHome;