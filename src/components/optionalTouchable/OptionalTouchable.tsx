import {
  GestureResponderEvent,
  StyleProp,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import React from 'react';

const OptionalTouchable = ({
  isTouchable,
  onPress,
  children,
  style,
}: {
  isTouchable: boolean;
  onPress?: (event: GestureResponderEvent) => void;
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
}) => {
  if (isTouchable) {
    return (
      <TouchableOpacity style={style} onPress={onPress}>
        {children}
      </TouchableOpacity>
    );
  } else {
    return children;
  }
};

export default OptionalTouchable;
