import React from 'react';
import {<PERSON><PERSON>, View} from 'tamagui';
import {c} from '../../utils/styleVariables';
import FontAwesomeIcon from 'react-native-vector-icons/FontAwesome6';
import MaterialIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import OnboardingChoiceWidget from '../../components/onboardingChoiceWidget/OnboardingChoiceWidget';
import {Screens, UserType} from '../../utils/enums';
import {useOnboarding} from '../../providers/OnboardingProvider';
import FormWrapper from '../formWrapper/FormWrapper';
import {navigate} from '../../navigation/navigation';
const OnboardingStepOne = () => {
  const {userType, setUserType, setOnboardingActiveStep} = useOnboarding();
  const handleContinue = () => {
    setOnboardingActiveStep(2);
    navigate(Screens.ONBOARDING_STEP_TWO);
  };
  return (
    <FormWrapper
      title="Cum dorești să folosești aplicația?"
      titleProps={{color: c.black}}
      backgroundColor={c.white}>
      <View alignItems="center" gap="$4">
        <OnboardingChoiceWidget
          onPress={() => setUserType(UserType.CLIENT)}
          isSelected={userType === UserType.CLIENT}
          icon={
            <FontAwesomeIcon
              name="car"
              color={userType === UserType.CLIENT ? c.white : c.black}
              size={30}
            />
          }
          text="Caut servicii auto"
        />
        <OnboardingChoiceWidget
          onPress={() => setUserType(UserType.COMPANY)}
          isSelected={userType === UserType.COMPANY}
          icon={
            <MaterialIcon
              name="account-wrench"
              color={userType === UserType.COMPANY ? c.white : c.black}
              size={30}
            />
          }
          text="Ofer servicii auto"
        />
        {!!userType && (
          <Button
            onPress={handleContinue}
            width={'80%%'}
            backgroundColor={c.brandBlue}
            color={c.white}
            marginTop="$5"
            alignSelf="center"
            size="$5">
            Continuă
          </Button>
        )}
      </View>
    </FormWrapper>
  );
};

export default OnboardingStepOne;
