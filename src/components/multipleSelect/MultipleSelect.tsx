import React, { useEffect, useMemo, useRef } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/FontAwesome6';
import { Adapt, Paragraph, Select, SelectItemParentProvider, SelectProvider, Sheet, Text, useSelectContext, useSelectItemParentContext, YStack } from 'tamagui';
import { c } from '../../utils/styleVariables';
import { FormikErrors } from 'formik';

const ChevronDown = () => <Icon name="chevron-down" color={'black'} size={16} />;

export function MultipleSelect<T extends string>({
	val,
	setVal,
	name,
	items,
	placeholder,
	triggerProps = {},
	errorText
}: {
	val: T[];
	setVal: React.Dispatch<React.SetStateAction<T[]>>;
	name: string;
	items: { name: T }[];
	placeholder: string | undefined;
	triggerProps?: React.ComponentProps<typeof Select.Trigger>;
	errorText?: string;
}) {
	const { bottom } = useSafeAreaInsets();

	const label = useMemo(() => {
		console.log('val', val);

		// Handle case where val is an array of objects with a 'name' property
		if (Array.isArray(val) && val.length > 0 && typeof val[0] === 'object' && 'name' in val[0]) {
			return val
				.map((item) => item.name) // Extract 'name' property
				.sort() // Sort alphabetically
				.join(', '); // Join into a string
		}

		// Fallback to original functionality for other cases
		return val.slice().sort().join(', ') || placeholder || 'Select an item';
	}, [val, placeholder]);

	const valueListenersRef = useRef<Set<(val: string) => void>>(new Set());

	useEffect(() => {
		setTimeout(() => {
			// need to notify the select items
			// whose values have changed
			valueListenersRef.current.forEach((listener) => listener('selected'));
		}, 0);
	}, [val]);

	return (
		<Select value={'selected'} onValueChange={() => {}} disablePreventBodyScroll>
			<OverrideSelectContext selectedItem={label}>
				<Select.Trigger
					// noTextWrap="all"
					// title={<Paragraph>{label}</Paragraph>}
					iconAfter={ChevronDown}
					{...triggerProps}
				>
					<Paragraph flexShrink={1} numberOfLines={1} size="$4">
						{label}
					</Paragraph>
				</Select.Trigger>
			</OverrideSelectContext>

			<Adapt platform="touch">
				<Sheet
					native={true}
					modal
					dismissOnSnapToBottom
					animationConfig={{
						type: 'spring',
						damping: 20,
						mass: 1.2,
						stiffness: 250
					}}
				>
					<Sheet.Frame>
						<Sheet.ScrollView>
							<Adapt.Contents />
						</Sheet.ScrollView>
					</Sheet.Frame>
					<Sheet.Overlay animation="lazy" enterStyle={{ opacity: 0 }} exitStyle={{ opacity: 0 }} />
				</Sheet>
			</Adapt>

			<Select.Content zIndex={200000}>
				<Select.Viewport minWidth={200}>
					<Select.Group pb={bottom}>
						<Select.Label>{name}</Select.Label>
						{/* for longer lists memoizing these is useful */}
						{useMemo(
							() =>
								items.map((item, i) => {
									return (
										<OverrideSelectParentContext
											key={`MultiSelect-${item.name}`}
											setOpen={() => {}}
											onChange={() => setVal({ name: item.name })}
											valueSubscribe={(listener) => {
												valueListenersRef.current.add(listener);
												return () => {
													valueListenersRef.current.delete(listener);
												};
											}}
										>
											<Select.Item
												index={i}
												value={val.findIndex((carMake) => carMake.name === item.name) >= 0 ? 'selected' : 'unselected'}
											>
												<Select.ItemText>{item.name}</Select.ItemText>
												<Select.ItemIndicator marginLeft="auto">
													<Icon name="check" size={18} color={c.brandBlue} />
												</Select.ItemIndicator>
											</Select.Item>
										</OverrideSelectParentContext>
									);
								}),
							[items, val]
						)}
					</Select.Group>
				</Select.Viewport>
			</Select.Content>
			{!!errorText && (
				<Text alignSelf="flex-start" color={c.warningOrange} fontWeight={600}>
					{errorText}
				</Text>
			)}
		</Select>
	);
}

const OverrideSelectParentContext = ({
	children,
	...overrides
}: Partial<ReturnType<typeof useSelectItemParentContext>> & {
	children: React.ReactNode;
}) => {
	const ctx = useSelectItemParentContext('OverrideSelectParentContext', undefined);
	const keys = useMemo(() => Object.keys(overrides).sort(), [overrides]);
	const overriddenCtx = useMemo(() => ({ ...ctx, ...overrides }), [ctx, ...keys.map((k) => overrides[k as keyof typeof overrides])]);

	return (
		<SelectItemParentProvider scope={undefined} {...overriddenCtx}>
			{children}
		</SelectItemParentProvider>
	);
};

const OverrideSelectContext = ({
	children,
	...overrides
}: Partial<ReturnType<typeof useSelectContext>> & {
	children: React.ReactNode;
}) => {
	const ctx = useSelectContext('OverrideSelectContext', undefined);
	const keys = useMemo(() => Object.keys(overrides).sort(), [overrides]);
	const overriddenCtx = useMemo(() => ({ ...ctx, ...overrides }), [ctx, ...keys.map((k) => overrides[k as keyof typeof overrides])]);

	return (
		<SelectProvider scope={undefined} {...overriddenCtx}>
			{children}
		</SelectProvider>
	);
};
