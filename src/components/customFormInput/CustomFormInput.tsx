import {StyleSheet} from 'react-native';
import React from 'react';
import {
  Input,
  InputProps,
  Label,
  LabelProps,
  Text,
  View,
  ViewProps,
} from 'tamagui';
import {c} from '../../utils/styleVariables';

const CustomFormInput = ({
  inputProps,
  labelProps,
  labelText,
  errorText,
  onChangeText,
  value,
  onBlur,
  containerProps,
  onPress,
}: {
  inputProps: InputProps;
  labelProps?: LabelProps;
  labelText?: string;
  errorText?: string | null;
  onChangeText: (e: any) => void;
  value: string;
  onBlur: (e: any) => void;
  containerProps?: ViewProps;
  onPress?: () => void;
}) => {
  return (
    <View width={'100%'} {...containerProps}>
      {!!labelText && <Label {...labelProps}>{labelText}</Label>}
      <Input
        {...inputProps}
        onChangeText={onChangeText}
        onBlur={onBlur}
        value={value}
      />
      {!!errorText && (
        <Text alignSelf="flex-start" color={c.cancelRed}>
          {errorText}
        </Text>
      )}
    </View>
  );
};

export default CustomFormInput;

const styles = StyleSheet.create({});
