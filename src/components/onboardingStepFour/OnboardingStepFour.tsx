import { StyleSheet, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import FormWrapper from '../formWrapper/FormWrapper';
import { FieldArray, Formik } from 'formik';
import { Button, Label, Text, XStack, YStack } from 'tamagui';
import CustomFormInput from '../customFormInput/CustomFormInput';
import { c } from '../../utils/styleVariables';
import OfferedService from '../offeredService/OfferedService';
import Icon from 'react-native-vector-icons/FontAwesome6';
import { addServicesSchema } from '../../utils/schemaValidation';
import { TimerPickerModal } from 'react-native-timer-picker';
import { navigate } from '../../navigation/navigation';
import { Screens, SubscriptionType, TabNames } from '../../utils/enums';
import { useUserInfo } from '../../providers/UserInfoProvider';
import BrandButton from '../brandButton/BrandButton';
import { useMutation, useQuery } from '@apollo/client';
import { GET_SERVICE_TYPES } from '../../queries/GET_SERVICE_TYPES';
import { STORE_SERVICES } from '../../queries/STORE_SERVICES';
import { GET_CAR_MAKES } from '../../queries/GET_CAR_MAKES';

const OnboardingStepFour = () => {
	const [serviceCount, setServiceCount] = useState(1);
	const [storeServices] = useMutation(STORE_SERVICES, {
		fetchPolicy: 'network-only'
	});

	const handleFormSubmit = async (values) => {
		console.log('submitting', values);
		const mappedServices = values.services.map((service) => ({
			serviceTypeId: service.offeredService,
			startTime: values.startTime,
			endTime: values.endTime,
			allowedCarMakes: service.carMakes.map((carMake) => carMake.name),
			price: parseInt(service.price)
		}));
		setUserProps({ services: mappedServices, appointments: parseInt(values.appointments) });
		await storeServices({
			variables: {
				services: mappedServices,
				userId: userInfo?.id,
				appointments: parseInt(values.appointments)
			},
			onCompleted: async ({ storeServices: storeServicesData }) => {
				if (storeServicesData.status === 200) {
					navigate('Tabs', { screenName: TabNames.HOME });
				} else {
					console.log('Error storing services: ', storeServicesData.message);
				}
			},
			onError: (e: any) => {
				console.error('Error storing services', e);
			}
		});
	};
	const handleAddService = () => {
		setServiceCount(serviceCount + 1);
	};
	const handleSubscribe = () => {};
	const [maxServices, setMaxServices] = useState(3);
	const isPro = false;
	const [showStartPicker, setShowStartPicker] = useState(false);
	const [showEndPicker, setShowEndPicker] = useState(false);
	const { userInfo, getUserInfo, setUserProps } = useUserInfo();
	const [serviceTypes, setServiceTypes] = useState([]);

	useEffect(() => {
		getUserInfo();
	}, []);
	useEffect(() => {
		if (userInfo?.subscriptionType === SubscriptionType.PRO) {
			setMaxServices(10);
		} else {
			setMaxServices(3);
		}
	}, [userInfo]);
	useQuery(GET_SERVICE_TYPES, {
		variables: {
			userId: userInfo?.id
		},
		fetchPolicy: 'network-only',
		onError: (err) => {
			console.log('Error getting service types', err);
		},
		onCompleted: async ({ getServiceTypes: getServiceTypesData }) => {
			if (getServiceTypesData.serviceTypes) {
				console.log('getServiceTypes successful');
				setServiceTypes(getServiceTypesData.serviceTypes.map((serviceType) => ({ id: serviceType.serviceTypeId, name: serviceType.name })));
			} else {
				console.log('Error getting service types');
			}
		}
	});

	const formatTime = ({ hours, minutes, seconds }: { hours?: number; minutes?: number; seconds?: number }) => {
		const timeParts = [];

		if (hours !== undefined) {
			timeParts.push(hours.toString().padStart(2, '0'));
		}
		if (minutes !== undefined) {
			timeParts.push(minutes.toString().padStart(2, '0'));
		}

		return timeParts.join(':');
	};
	const goToSubscriptionPage = () => {
		navigate('Subscribe', { canGoBack: true });
	};

	const [carMakes, setCarMakes] = useState([]);

	useQuery(GET_CAR_MAKES, {
		variables: {
			userId: userInfo?.id
		},
		fetchPolicy: 'network-only',
		onError: (err) => {
			console.log('Error getting car makes', err);
		},
		onCompleted: async ({ getCarMakes: getCarMakesData }) => {
			if (getCarMakesData.carMakes) {
				console.log('getCarMakes successful');
				setCarMakes(getCarMakesData.carMakes.map((carMake) => ({ id: carMake.id, name: carMake.name })));
			} else {
				console.log('Error getting car makes');
			}
		}
	});

	return (
		<FormWrapper titleProps={{ margin: 0 }} title="Ajută-ne să aflăm mai multe despre tine">
			<Formik
				validateOnChange={false}
				validationSchema={addServicesSchema}
				initialValues={{
					startTime: '',
					endTime: '',
					appointments: '1',
					services: [
						{
							offeredService: '',
							carMakes: [],
							price: ''
						}
					]
				}}
				onSubmit={(values) => handleFormSubmit(values)}
			>
				{({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
					<YStack backgroundColor={c.white} alignItems="flex-start" gap="$4" paddingVertical="$4" paddingHorizontal="$4" borderRadius="$6">
						<YStack width={'100%'}>
							{console.log('errors', errors)}
							<Label>Ore funcționare</Label>
							<XStack gap={'$4'} alignItems="center">
								<TimerPickerModal
									visible={showStartPicker}
									setIsVisible={setShowStartPicker}
									onConfirm={(pickedDuration) => {
										handleChange('startTime')(formatTime(pickedDuration));
										setShowStartPicker(false);
									}}
									modalTitle="Oră start"
									onCancel={() => setShowStartPicker(false)}
									closeOnOverlayPress
									hideSeconds
									styles={{
										theme: 'dark'
									}}
									modalProps={{
										overlayOpacity: 0.2
									}}
								/>
								<TimerPickerModal
									visible={showEndPicker}
									setIsVisible={setShowEndPicker}
									onConfirm={(pickedDuration) => {
										handleChange('endTime')(formatTime(pickedDuration));
										setShowEndPicker(false);
									}}
									modalTitle="Oră sfârșit"
									onCancel={() => setShowEndPicker(false)}
									closeOnOverlayPress
									hideSeconds
									styles={{
										theme: 'dark'
									}}
									modalProps={{
										overlayOpacity: 0.2
									}}
								/>
								<Button onPress={() => setShowStartPicker(true)}>De la</Button>
								<Text>{values.startTime}</Text>
								<Button onPress={() => setShowEndPicker(true)}>Până la</Button>
								<Text>{values.endTime}</Text>
							</XStack>
						</YStack>
						<YStack width={'100%'} alignItems="flex-start" gap={5}>
							<View>
								{userInfo?.subscriptionType !== SubscriptionType.PRO ? (
									<BrandButton title="Fă upgrade la PRO" size="$2" onPress={goToSubscriptionPage} />
								) : (
									<BrandButton title="Doar PRO" size="$2" disabled />
								)}
							</View>
							<YStack
								width={'100%'}
								borderColor={c.brandBlue}
								backgroundColor={c.brandBlueLowOpacity}
								borderWidth={1}
								borderRadius={14}
								padding={10}
							>
								<XStack justifyContent="space-between">
									<Label>Număr programări în paralel</Label>
								</XStack>
								<CustomFormInput
									inputProps={{
										disabled: userInfo?.subscriptionType !== SubscriptionType.PRO,
										id: 'appointments',
										borderWidth: 2,
										placeholder: 'Nr. programări'
									}}
									errorText={errors.appointments && touched.appointments ? errors.appointments : null}
									onChangeText={handleChange('appointments')}
									onBlur={handleBlur('appointments')}
									value={values.appointments}
								/>
							</YStack>
						</YStack>
						<YStack width={'100%'} gap={'$2'}>
							<FieldArray
								name="services"
								validateOnChange={false}
								render={(arrayHelpers) => (
									<>
										{values.services.map((service, index) => (
											<>
												<XStack alignItems="center" justifyContent="space-between" marginHorizontal={10}>
													<Text key={`>Serviciul ${index + 1}`}>Serviciul {index + 1}</Text>
													{values.services.length > 1 && (
														<TouchableOpacity
															onPress={() => {
																if (values.services.length > 1) {
																	arrayHelpers.remove(index);
																}
															}}
														>
															<Icon name="trash-can" color="#E34343" size={21} />
														</TouchableOpacity>
													)}
												</XStack>
												<OfferedService
													carMakes={carMakes}
													serviceTypes={serviceTypes}
													key={`OfferedService-${index}`}
													index={index}
													handleChange={handleChange}
													handleBlur={handleBlur}
													values={values}
													errors={errors?.services?.[index]}
													touched={touched?.services?.[index]}
												/>
											</>
										))}
										{values.services.length < maxServices ? (
											<Button
												onPress={() => {
													handleAddService();
													arrayHelpers.push({
														offeredService: '',
														carMakes: [],
														price: ''
													});
												}}
												width={'100%'}
												backgroundColor={c.brandBlue}
												color={c.white}
												marginTop="$5"
												alignSelf="center"
												size="$5"
												icon={<Icon name="plus" size={20} />}
											>
												Adaugă încă un serviciu
											</Button>
										) : (
											!isPro && (
												<Button
													onPress={handleSubscribe}
													width={'100%'}
													backgroundColor={c.brandBlue}
													color={c.white}
													marginTop="$5"
													alignSelf="center"
													size="$5"
												>
													Treci la PRO pentru a adaugă servicii
												</Button>
											)
										)}
									</>
								)}
							/>
						</YStack>
						{(errors?.startTime || errors?.endTime) && <Text color={c.cancelRed}>{errors?.startTime || errors?.endTime}</Text>}
						<Button onPress={handleSubmit} width={'100%'} backgroundColor={c.black} color={c.white} marginTop="$5" alignSelf="center" size="$5">
							Continuă
						</Button>
					</YStack>
				)}
			</Formik>
		</FormWrapper>
	);
};

export default OnboardingStepFour;

const styles = StyleSheet.create({});
