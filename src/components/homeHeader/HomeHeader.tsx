import { Image, Text, View } from "tamagui";
import Hello from './../../assets/images/png/hello-icon.png'
import Notification from './../../assets/images/png/notification-icon.png'

const UserHeader = () => {

    return (
        <View
            display={'flex'}
            flexDirection={'row'}
            justifyContent={'space-between'}
            alignItems={'center'}
            padding={30}
        >
            <View
                display={'flex'}
                flexDirection={'row'}
                gap={20}
            >
                <Image width={48} height={50} src={Hello} />
                <View>
                    <Text fontSize={22}>Bun venit,</Text>
                    <Text fontSize={25}>Andrei</Text>
                </View>
            </View>
            <Image width={22} height={28} src={Notification} />
        </View>
    )
}

export default UserHeader;