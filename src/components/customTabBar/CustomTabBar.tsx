import { EmitterSubscription, Keyboard, Platform, TouchableHighlight } from 'react-native';
import React, { useEffect, useState } from 'react';
import { TabDetails, TabNames } from '../../utils/enums';
import TabItem from './tabItem/TabItem';

import { View } from 'tamagui';

const CustomTabBar = ({ state, navigation }: { state: any; navigation: any }) => {

    const tabMapping = {
        [TabNames.HOME]: TabDetails.HOME,
        [TabNames.SCHEDULE]: TabDetails.SCHEDULE,
        [TabNames.PROFILE]: TabDetails.PROFILE,
    };
    const [visible, setVisible] = useState(true);
    useEffect(() => {
        let keyboardEventListeners: Array<EmitterSubscription>;
        if (Platform.OS === 'android') {
            keyboardEventListeners = [
                Keyboard.addListener('keyboardDidShow', () => setVisible(false)),
                Keyboard.addListener('keyboardDidHide', () => setTimeout(() => setVisible(true), 5)),
            ];
        }
        return () => {
            if (Platform.OS === 'android') {
                keyboardEventListeners?.forEach((eventListener) => eventListener.remove());
            }
        };
    }, []);

    return (
        <View
            display={'flex'}
            justifyContent={'space-evenly'}
            alignItems={'center'}
            backgroundColor={'#ffffff'}
            paddingBottom={15}
            flexDirection={'row'}
            style={[!visible ? { display: 'none' } : { display: 'flex' }]}>
            {state.routes.map((route: { name: TabNames; key: any }, index: number) => {
                const label = route.name;

                const isFocused = state.index === index;

                const onPress = () => {
                    const event = navigation.emit({
                        type: 'tabPress',
                        target: route.key,
                    });

                    if (!isFocused && !event.defaultPrevented) {
                        navigation.navigate(route.name);
                    }
                };

                return (
                    <View
                        key={index}
                        display={'flex'}
                        borderRadius={20}
                        padding={10}
                        height={70}
                        width={100}
                        alignItems={'center'}
                        justifyContent={'center'}
                        onPress={onPress}
                    >
                        <TabItem item={tabMapping[label]} isFocused={isFocused} />
                    </View>
                )
            })}
        </View>

    );
};

export default CustomTabBar;
