import React from 'react';

import { Image, Text, View } from 'tamagui';

const TabItem = ({ item, isFocused }: { item: any; isFocused: boolean }) => {
    return (
        <View
            display={'flex'}
            flexDirection={'column'}
            alignItems={'center'}
            justifyContent={'center'}
        >
            <View
                flex={1}
                alignItems={'center'}
                justifyContent={'center'}
                height={80}
                width={80}
                borderRadius={20}
                backgroundColor={isFocused ? '#8ED2FF' : '#ffffff'}
            >
                <Image
                    width={20}
                    height={20}
                    src={item.icon}
                />
            </View>

            <Text
                color={'#000000'}
                fontSize={11}
                fontWeight={600}
                marginTop={2}
            >
                {item.name}
            </Text>
        </View>
    )
};

export default TabItem;
