import { Text, Separator, SizeTokens, Switch, XStack } from 'tamagui';

function SwitchWithLabel(props: { size: SizeTokens; defaultChecked?: boolean; label: string; checked: boolean; onCheckedChange: (value: boolean) => void }) {
	const id = `switch-${props.size.toString().slice(1)}-${props.defaultChecked ?? ''}}`;
	return (
		<XStack width={200} alignItems="center" gap="$4">
			<Text paddingRight="$0" minWidth={90} justifyContent="flex-end" htmlFor={id}>
				{props.label}
			</Text>
			<Separator minHeight={20} vertical />
			<Switch id={id} size={props.size} defaultChecked={props.defaultChecked} checked={props.checked} onCheckedChange={props.onCheckedChange}>
				<Switch.Thumb animation="quicker" />
			</Switch>
		</XStack>
	);
}

export default SwitchWithLabel;
