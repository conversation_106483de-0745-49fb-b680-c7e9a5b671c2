import React from 'react';
import { <PERSON><PERSON>, YStack } from 'tamagui';
import FormWrapper from '../formWrapper/FormWrapper';
import { Formik } from 'formik';
import { clientDetailsSchema, companyDetailsSchema } from '../../utils/schemaValidation';
import { c } from '../../utils/styleVariables';
import { useOnboarding } from '../../providers/OnboardingProvider';
import OnboardingClientInputs from '../onboardingClientInputs/OnboardingClientInputs';
import { Screens, TabNames, UserType } from '../../utils/enums';
import OnboardingCompanyInputs from '../onboardingCompanyInputs/OnboardingCompanyInputs';
import { navigate } from '../../navigation/navigation';
import { useMutation } from '@apollo/client';
import { UPDATE_USER_INFO } from '../../queries/UPDATE_USER_INFO';
import { useAuth } from '../../providers/AuthProvider';
import { useUserInfo } from '../../providers/UserInfoProvider';

const OnboardingStepTwo = () => {
	const { setClientOnboardingInfo, setCompanyOnboardingInfo, userType, userSubscriptionDetails } = useOnboarding();
	const { user } = useAuth();
	const { userInfo } = useUserInfo();

	const [updateUser] = useMutation(UPDATE_USER_INFO, {
		fetchPolicy: 'network-only'
	});
	const handleFormSubmit = (values: any) => {
		console.log('form submit', values);
		console.log('user type', userType);
		if (userType === UserType.COMPANY || userInfo?.userType === UserType.COMPANY) {
			setCompanyOnboardingInfo(values);

			updateUser({
				variables: {
					user: {
						userId: user?.id,
						userType,
						companyName: values.companyName,
						representativeFirstName: values.firstNameRepresentative,
						representativeLastName: values.lastNameRepresentative,
						phone: values.companyPhone,
						address: values.companyAddress,
						coordinates: {
							lat: values.lat.toString(),
							lng: values.lng.toString()
						},
						administrativeArea: values.administrativeArea,
						country: userSubscriptionDetails.country,
						city: userSubscriptionDetails.city,
						street: userSubscriptionDetails.street,
						streetNumber: userSubscriptionDetails.streetNumber,
						postalCode: userSubscriptionDetails.postalCode,
						state: userSubscriptionDetails.state,
						taxId: values?.taxId
					}
				},
				onCompleted: ({ updateUser: updateUserData }) => {
					if (updateUserData.status !== 500) {
						console.log('User updated successfully');
						navigate(Screens.ONBOARDING_STEP_THREE);
					} else {
						console.log('Failed to update user');
					}
				},
				onError: (e: any) => {
					console.log('Error updating user', e);
				}
			});
		} else {
			setClientOnboardingInfo(values);
			updateUser({
				variables: {
					user: {
						userId: user?.id,
						userType,
						firstName: values.firstName,
						lastName: values.lastName,
						phone: values.phone,
						address: values.address,
						coordinates: {
							lat: values.lat.toString(),
							lng: values.lng.toString()
						},
						administrativeArea: values.administrativeArea
					}
				},
				onCompleted: ({ updateUser: updateUserData }) => {
					if (updateUserData.status !== 500) {
						console.log('User updated successfully');
						navigate('Tabs', { screen: TabNames.HOME });
					} else {
						console.log('Failed to update user');
					}
				},
				onError: (e: any) => {
					console.log('Error updating user', e);
				}
			});
		}
	};
	return (
		<FormWrapper titleProps={{ margin: 0 }} title="Ajută-ne să aflăm mai multe despre tine">
			<Formik
				validationSchema={userType === UserType.CLIENT ? clientDetailsSchema : companyDetailsSchema}
				initialValues={{
					lastName: '',
					firstName: '',
					phone: '',
					address: '',
					companyPhone: '',
					companyAddress: '',
					companyName: '',
					lastNameRepresentative: '',
					firstNameRepresentative: '',
					lat: '0',
					lng: '0',
					administrativeArea: '',
					companyPostalCode: '',
					taxIdCollection: false,
					taxId: ''
				}}
				onSubmit={(values) => handleFormSubmit(values)}
			>
				{({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
					<YStack flex={1} backgroundColor={c.white} alignItems="center" gap="$4" paddingVertical="$4" paddingHorizontal="$4" borderRadius="$6">
						{userType === UserType.CLIENT ? (
							<OnboardingClientInputs handleChange={handleChange} handleBlur={handleBlur} values={values} errors={errors} touched={touched} />
						) : (
							<OnboardingCompanyInputs handleChange={handleChange} handleBlur={handleBlur} values={values} errors={errors} touched={touched} />
						)}

						<Button onPress={handleSubmit} width={'100%'} backgroundColor={c.brandBlue} color={c.white} marginTop="$5" alignSelf="center" size="$5">
							Continuă
						</Button>
					</YStack>
				)}
			</Formik>
		</FormWrapper>
	);
};

export default OnboardingStepTwo;
