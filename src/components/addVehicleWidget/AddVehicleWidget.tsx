import { Image, Text, View } from "tamagui";
import Car from './../../assets/images/png/widget-car-icon.png'
import RightArrow from './../../assets/images/png/widget-right-arrow.png'

const AddVehicleWidget = () => {
    return (
        <View
            display={'flex'}
            flexDirection={'row'}
            justifyContent={'space-between'}
            alignItems={'center'}
            paddingHorizontal={15}
            backgroundColor={'#8ED2FF'}
            borderRadius={20}
            maxHeight={130}
            gap={10}
        >
            <View
                display={'flex'}
                flexDirection={'column'}
                flex={2}
            >
                <View
                    display={'flex'}
                    flexDirection={'row'}
                    gap={10}
                    justifyContent={'flex-start'}
                    alignItems={'center'}
                    marginBottom={10}
                >
                    <Text
                        fontSize={15}
                        fontWeight={700}
                    >Adaugă-ți autovehiculul</Text>
                    <Image width={10} height={15} src={RightArrow} />
                </View>
                <Text
                    fontSize={12}
                    fontWeight={400}
                    color={'#3B3B3B'}
                >
                    Adaugă detaliile autovehiculului tău pentru a vedea doar servicii relevante și pentru a finaliza programările rapid
                </Text>
            </View>
            <View
                display={'flex'}
                flex={1}
            >
                <Image width={'130%'} height={'130%'} src={Car} />
            </View>
        </View>
    )

}

export default AddVehicleWidget;