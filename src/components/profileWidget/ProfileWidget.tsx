import { Image, Text, View } from 'tamagui';
import { ProfileWidgetProps } from '../../common/interfaces';
import { TouchableOpacity } from 'react-native';
import { navigate } from '../../navigation/navigation';
import { c } from '../../utils/styleVariables';
import Icon from 'react-native-vector-icons/FontAwesome6';

const ProfileWidget = ({ name, icon, navigateToScreen, onPress }: ProfileWidgetProps) => {
	return (
		<TouchableOpacity onPress={() => (navigateToScreen ? navigate(navigateToScreen) : onPress?.())}>
			<View
				display={'flex'}
				flexDirection={'row'}
				justifyContent={'space-between'}
				alignItems={'center'}
				borderWidth={1}
				borderStyle={'solid'}
				borderColor={c.brandBlue}
				backgroundColor={c.brandBlueLowOpacity}
				borderRadius={14}
				padding={10}
			>
				<View display={'flex'} flexDirection={'row'} alignItems={'center'} gap={10}>
					{icon && <Image height={22} width={22} src={icon} />}
					<Text fontSize={16} fontWeight={500}>
						{name}
					</Text>
				</View>
				<Icon name="chevron-right" color={'black'} size={20} />
			</View>
		</TouchableOpacity>
	);
};

export default ProfileWidget;
