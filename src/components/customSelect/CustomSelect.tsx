import { StyleSheet, View } from 'react-native';
import React, { useState } from 'react';
import { Adapt, Select, Sheet, YStack, Text } from 'tamagui';
import Icon from 'react-native-vector-icons/FontAwesome6';
import { c } from '../../utils/styleVariables';

const ChevronDown = () => <Icon name="chevron-down" color={'black'} size={16} />;

const CustomSelect = ({ data, ...props }) => {
	return (
		<Select value={props.value} onValueChange={props.setVal} disablePreventBodyScroll {...props}>
			<Select.Trigger width={'100%'} iconAfter={ChevronDown}>
				<Select.Value placeholder={props.placeholder} />
			</Select.Trigger>

			<Adapt platform="touch">
				<Sheet
					native={true}
					modal
					dismissOnSnapToBottom
					animationConfig={{
						type: 'spring',
						damping: 20,
						mass: 1.2,
						stiffness: 250
					}}
				>
					<Sheet.Frame>
						<Sheet.ScrollView>
							<Adapt.Contents />
						</Sheet.ScrollView>
					</Sheet.Frame>
					<Sheet.Overlay animation="lazy" enterStyle={{ opacity: 0 }} exitStyle={{ opacity: 0 }} />
				</Sheet>
			</Adapt>

			<Select.Content zIndex={200000}>
				<Select.Viewport minWidth={200}>
					<Select.Group>
						{React.useMemo(
							() =>
								data?.map((item, i) => {
									return (
										<Select.Item index={i} key={item.name} value={item.id} onPress={() => setVal(item.id)}>
											<Select.ItemText marginRight="auto">{item.name}</Select.ItemText>
											<Select.ItemIndicator>
												<Icon name="check" size={18} color={c.brandBlue} />
											</Select.ItemIndicator>
										</Select.Item>
									);
								}),
							[data]
						)}
					</Select.Group>
					{/* Native gets an extra icon */}

					<YStack
						position="absolute"
						right={0}
						top={0}
						bottom={0}
						alignItems="center"
						justifyContent="center"
						width={'$4'}
						pointerEvents="none"
					></YStack>
				</Select.Viewport>

				<Select.ScrollDownButton alignItems="center" justifyContent="center" position="relative" width="100%" height="$3">
					<YStack zIndex={10}></YStack>
				</Select.ScrollDownButton>
			</Select.Content>
			{!!props?.errorText && (
				<Text alignSelf="flex-start" color={c.warningOrange} fontWeight={600}>
					{props.errorText}
				</Text>
			)}
		</Select>
	);
};

export default CustomSelect;

const styles = StyleSheet.create({});
