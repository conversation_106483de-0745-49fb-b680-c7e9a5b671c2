import {Image, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {Button, XStack} from 'tamagui';
import {AppleLogo, GoogleLogo} from '../../assets';
import {useAuth} from '../../providers/AuthProvider';
import {CognitoProviders} from '../../utils/enums';

const SocialLoginButtons = () => {
  const {loginWithProvider} = useAuth();

  return (
    <XStack alignItems="center" justifyContent="center" gap={'$3'}>
      <Button
        borderColor={'black'}
        backgroundColor={'transparent'}
        alignSelf="center"
        circular
        size="$6"
        icon={<Image source={GoogleLogo} style={{width: 20, height: 20}} />}
        onPress={async () =>
          await loginWithProvider(CognitoProviders.GOOGLE)
        }></Button>
      <Button
        borderColor={'black'}
        backgroundColor={'transparent'}
        alignSelf="center"
        circular
        size="$6"
        icon={<Image source={AppleLogo} style={{width: 20, height: 25}} />}
        onPress={async () =>
          await loginWithProvider(CognitoProviders.APPLE)
        }></Button>
    </XStack>
  );
};

export default SocialLoginButtons;

const styles = StyleSheet.create({});
