import {TouchableOpacity} from 'react-native';
import React from 'react';
import {Text, View} from 'tamagui';
import {c} from '../../utils/styleVariables';
import Icon from 'react-native-vector-icons/FontAwesome6';

interface OnboardingChoiceWidgetProps {
  isSelected: boolean;
  icon: React.ReactNode;
  text: string;
  onPress: () => void;
}

const OnboardingChoiceWidget = ({
  isSelected = false,
  icon,
  text,
  onPress,
}: OnboardingChoiceWidgetProps) => {
  return (
    <TouchableOpacity style={{width: '80%'}} onPress={onPress}>
      <View
        style={
          isSelected
            ? {backgroundColor: c.brandBlue}
            : {
                backgroundColor: c.brandBlueLowOpacity,
                borderColor: c.brandBlue,
                borderWidth: 1,
              }
        }
        paddingHorizontal="$4"
        paddingVertical="$10"
        alignItems="center"
        borderRadius="$4">
        {isSelected && (
          <Icon
            name="check-circle"
            size={24}
            color={c.lightGreen}
            style={{position: 'absolute', right: 5, top: 5}}
          />
        )}
        {icon}
        <Text color={isSelected ? c.white : c.black}>{text}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default OnboardingChoiceWidget;
