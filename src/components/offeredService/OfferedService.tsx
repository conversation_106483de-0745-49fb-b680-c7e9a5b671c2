import { Label, YStack } from 'tamagui';
import CustomSelect from '../customSelect/CustomSelect';
import CustomFormInput from '../customFormInput/CustomFormInput';
import { c } from '../../utils/styleVariables';
import { MultipleSelect } from '../multipleSelect/MultipleSelect';
import React, { useState } from 'react';
import { FieldArray } from 'formik';
import { useQuery } from '@apollo/client';
import { GET_CAR_MAKES } from '../../queries/GET_CAR_MAKES';
import { useUserInfo } from '../../providers/UserInfoProvider';

const OfferedService = (props) => {
	return (
		<YStack width={'100%'} backgroundColor={c.brandBlue} borderRadius={14} padding={10} borderColor={c.lightGray2}>
			<Label color="white">Serviciul oferit</Label>

			<CustomSelect
				value={props.values.services[props.index].offeredService}
				setVal={props.handleChange(`services[${props.index}].offeredService`)}
				placeholder="Alege serviciul oferit"
				data={props?.serviceTypes}
				errorText={props.errors?.offeredService}
			/>
			<Label color="white">Mărci mașini pentru care se oferă serviciul</Label>
			<FieldArray
				validateOnChange={false}
				name={`services[${props.index}].carMakes`}
				key={`services[${props.index}].carMakes`}
				render={(arrayHelpers) => (
					<MultipleSelect
						key={'MultiSelect-carMakes'}
						val={props.values.services[props.index].carMakes}
						// setVal={props.handleChange(`services[${props.index}].carMakes`)}
						setVal={(item) => {
							const carMakeIndex = props.values.services[props.index].carMakes.findIndex((carMake) => carMake.name === item.name);
							console.log('carMakeIndex', carMakeIndex);

							carMakeIndex >= 0 ? arrayHelpers.remove(carMakeIndex) : arrayHelpers.push({ name: item.name });
							// arrayHelpers.push({name: value})}
						}}
						items={[{ id: 0, name: 'Toate' }, ...props.carMakes]}
						placeholder="Alege mărcile dorite"
						errorText={props.errors?.carMakes}
					/>
				)}
			/>
			{/* Multiselect pt subservicii */}
			<Label color="white">Preț serviciu (opțional)</Label>

			<CustomFormInput
				inputProps={{
					id: 'price',
					borderWidth: 2,
					placeholder: 'Preț serviciu'
				}}
				errorText={props.errors?.price && props.touched?.price ? props.errors.price : null}
				onChangeText={props.handleChange(`services[${props.index}].price`)}
				onBlur={props.handleBlur(`services[${props.index}].price`)}
				value={props.values.services[props.index].price}
			/>
		</YStack>
	);
};
export default OfferedService;
