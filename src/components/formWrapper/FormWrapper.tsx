import {StyleSheet, TouchableOpacity} from 'react-native';
import React from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {c} from '../../utils/styleVariables';
import {Text, TextProps} from 'tamagui';
import Icon from 'react-native-vector-icons/FontAwesome6';
import {canGoBack, navigation} from '../../navigation/navigation';

const FormWrapper = ({
  children,
  title,
  subtitle,
  HeaderComponent,
  titleProps,
  subtitleProps,
  goBack,
  backgroundColor,
}: {
  children: React.ReactNode;
  title: string | React.ReactNode;
  subtitle?: string | React.ReactNode;
  HeaderComponent?: React.ReactNode;
  titleProps?: TextProps;
  subtitleProps?: TextProps;
  goBack?: boolean;
  backgroundColor?: string;
}) => {
  const handleGoBack = () => {
    if (canGoBack()) navigation.goBack();
  };
  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: backgroundColor || c.brandBlue,
      }}>
      <KeyboardAwareScrollView
        contentContainerStyle={{
          // flex: 1,
          justifyContent: 'flex-start',
          paddingHorizontal: 20,
        }}>
        {goBack && (
          <TouchableOpacity onPress={handleGoBack}>
            <Icon name="chevron-left" color={'white'} size={22} />
          </TouchableOpacity>
        )}
        {HeaderComponent}
        <Text
          marginVertical="$2"
          fontWeight={700}
          fontSize={'$6'}
          color={c.white}
          {...titleProps}>
          {title}
        </Text>
        <Text
          marginTop="$2"
          marginBottom="$4"
          color={c.lighterGray}
          {...subtitleProps}>
          {subtitle}
        </Text>
        {children}
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default FormWrapper;

const styles = StyleSheet.create({});
