import { Image, Text, View } from "tamagui";
import { ServiceWidgetParams } from "../../common/interfaces";

import RightArrow from './../../assets/images/png/widget-right-arrow.png'

const ServiceWidget = ({ name, address }: ServiceWidgetParams) => {


    return (
        <View
            display={'flex'}
            flexDirection={'row'}
            justifyContent={'space-between'}
            alignItems={'center'}
            borderWidth={1}
            borderStyle={'solid'}
            borderColor={'#8ED2FF'}
            backgroundColor={'#8ed2ff3f'}
            borderRadius={20}
            padding={15}

            onPress={() => { }}
        >
            <View
                display={'flex'}
                flexDirection={'row'}
                justifyContent={'center'}
                alignItems={'center'}
                gap={10}
            >
                <View
                    backgroundColor={'#000000'}
                    height={50}
                    width={50}
                    borderRadius={10}
                >

                </View>
                <View
                    display={'flex'}
                    flexDirection={'column'}
                    gap={3}
                >
                    <Text
                        fontSize={20}
                        fontWeight={700}
                    >
                        {name}
                    </Text>
                    <Text
                        fontSize={14}
                        fontWeight={400}
                    >
                        {address}
                    </Text>
                </View>
            </View>

            <Image width={15} height={25} src={RightArrow} />
        </View>
    )
}

export default ServiceWidget;