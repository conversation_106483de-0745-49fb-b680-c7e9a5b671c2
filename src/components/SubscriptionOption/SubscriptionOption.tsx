import { Dimensions } from 'react-native';
import { <PERSON><PERSON>, Card, H2, H3, Text, View } from 'tamagui';
import { c } from '../../utils/styleVariables';
import BrandButton from '../brandButton/BrandButton';
import { SubscriptionType } from '../../utils/enums';

const CARD_WIDTH = Dimensions.get('window').width * 0.8;

function SubscriptionOption({ title, benefitsList, price, isPro, selectionButtonTitle, onPress, userActiveSubsType }) {
	return (
		<View flex={1} alignItems="center" gap={20} justifyContent="space-between">
			<View gap={30}>
				<Card
					backgroundColor={c.white}
					style={{
						shadowColor: isPro ? c.brandBlue : 'white',
						shadowOffset: {
							height: 0,
							width: 0
						},
						shadowOpacity: 0.4,
						shadowRadius: 8,
						elevation: 8
					}}
					elevate
					bordered
					width={CARD_WIDTH}
					maxHeight={400}
					marginTop={20}
					paddingVertical={20}
					paddingHorizontal={10}
					borderRadius={20}
					justifyContent="flex-start"
				>
					<Card.Header flexDirection="row" justifyContent="space-between">
						<H2>{title}</H2>
						{isPro && (
							<View borderWidth={1} borderColor={c.black} borderRadius={12} alignItems="center" justifyContent="center" padding={4}>
								<Text fontSize={12}>Cel mai popular</Text>
							</View>
						)}
					</Card.Header>
					<View>
						<H3 fontWeight={'bold'}>{price}</H3>
						{benefitsList.map((benefit) => (
							<Text marginVertical={10} fontSize={15}>{`\u2022 ${benefit}`}</Text>
						))}
					</View>
				</Card>
				{isPro && userActiveSubsType !== SubscriptionType.PRO && (
					<BrandButton title={'Încearcă gratuit timp de 30 de zile'} onPress={onPress}></BrandButton>
				)}
			</View>
			{!((userActiveSubsType === SubscriptionType.PRO && isPro) || (userActiveSubsType === SubscriptionType.BASIC && !isPro)) ? (
				<View>
					<Button
						onPress={onPress}
						alignSelf="center"
						size="$5"
						width={'$20'}
						// width="90%"
						style={{
							backgroundColor: 'black',
							marginVertical: 10,
							color: 'white'
						}}
					>
						{selectionButtonTitle}
					</Button>
					{isPro && (
						<Text alignSelf="center" color="gray" fontSize={13} marginTop={5}>
							Anulează oricând!
						</Text>
					)}
				</View>
			) : (
				<View marginBottom={30} backgroundColor={c.brandBlueLowOpacity} padding={10} borderRadius={14} borderColor={c.brandBlue} borderWidth={1}>
					<Text fontSize={16} fontWeight={600}>
						Plan activ
					</Text>
				</View>
			)}
		</View>
	);
}

export default SubscriptionOption;
