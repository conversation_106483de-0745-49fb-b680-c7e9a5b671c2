import { StyleSheet } from 'react-native';
import React, { ChangeEvent } from 'react';
import CustomFormInput from '../customFormInput/CustomFormInput';
import { FormikTouched } from 'formik';
import GoogleSearchInput from '../googleSearchInput/GoogleSearchInput';
import { UserType } from '../../utils/enums';

type GenericChangeEvent = {
	(e: ChangeEvent<any>): void;
	<T = string | ChangeEvent<any>>(field: T): T extends ChangeEvent<any> ? void : (e: string | ChangeEvent<any>) => void;
};

interface OnboardingClientInputsProps {
	handleChange: GenericChangeEvent;
	handleBlur: GenericChangeEvent;
	values: { [key: string]: string };
	errors: { [key: string]: string };
	touched: FormikTouched<{
		lastName: string;
		firstName: string;
		phone: string;
		address: string;
	}>;
}

const OnboardingClientInputs = ({ handleChange, handleBlur, values, errors, touched }: OnboardingClientInputsProps) => {
	console.log('values', values);
	return (
		<>
			<CustomFormInput
				inputProps={{
					width: '100%',
					id: 'lastName-onboarding',
					size: '$4',
					borderWidth: 2,
					placeholder: 'Nume',
					textContentType: 'name'
				}}
				errorText={errors.lastName && touched.lastName ? errors.lastName : null}
				onChangeText={handleChange('lastName')}
				onBlur={handleBlur('lastName')}
				value={values.lastName}
			/>
			<CustomFormInput
				inputProps={{
					width: '100%',
					id: 'firstName-onboarding',
					size: '$4',
					borderWidth: 2,
					placeholder: 'Prenume',
					textContentType: 'name'
				}}
				errorText={errors.firstName && touched.firstName ? errors.firstName : null}
				onChangeText={handleChange('firstName')}
				onBlur={handleBlur('firstName')}
				value={values.firstName}
			/>
			<CustomFormInput
				inputProps={{
					width: '100%',
					id: 'phone-onboarding',
					size: '$4',
					borderWidth: 2,
					placeholder: 'Telefon',
					textContentType: 'telephoneNumber'
				}}
				errorText={errors.phone && touched.phone ? errors.phone : null}
				onChangeText={handleChange('phone')}
				onBlur={handleBlur('phone')}
				value={values.phone}
			/>
			<GoogleSearchInput
				userType={UserType.CLIENT}
				inputProps={{
					width: '100%',
					id: 'address-onboarding',
					size: '$4',
					borderWidth: 2,
					placeholder: 'Adresă',
					textContentType: 'fullStreetAddress'
				}}
				errorText={errors.address && touched.address ? errors.address : null}
				onBlur={handleBlur('address')}
			/>
		</>
	);
};

export default OnboardingClientInputs;

const styles = StyleSheet.create({});
