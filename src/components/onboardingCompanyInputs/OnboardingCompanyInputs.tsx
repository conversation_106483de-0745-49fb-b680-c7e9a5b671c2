import { StyleSheet } from 'react-native';
import React, { ChangeEvent, useState } from 'react';
import CustomFormInput from '../customFormInput/CustomFormInput';
import { FormikTouched, useFormikContext } from 'formik';
import GoogleSearchInput from '../googleSearchInput/GoogleSearchInput';
import { useOnboarding } from '../../providers/OnboardingProvider';
import SwitchWithLabel from '../switchWithLabel/SwitchWithLabel';
import { Label, Text, YStack } from 'tamagui';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { UserType } from '../../utils/enums';

type GenericChangeEvent = {
	(e: ChangeEvent<any>): void;
	<T = string | ChangeEvent<any>>(field: T): T extends ChangeEvent<any> ? void : (e: string | ChangeEvent<any>) => void;
};

interface OnboardingCompanyInputsProps {
	handleChange: GenericChangeEvent;
	handleBlur: GenericChangeEvent;
	values: { [key: string]: any };
	errors: { [key: string]: string };
	touched: FormikTouched<{
		companyName: string;
		companyPhone: string;
		companyAddress: string;
		companyPostalCode: string;
		lastNameRepresentative: string;
		firstNameRepresentative: string;
		taxId: string;
	}>;
	isOnboarding: boolean;
}

const OnboardingCompanyInputs = ({ handleChange, handleBlur, values, errors, touched, isOnboarding = true }: OnboardingCompanyInputsProps) => {
	const { setFieldValue } = useFormikContext();
	const { userInfo } = useUserInfo();
	const { setUserSubscriptionDetails } = useOnboarding();
	return (
		<>
			<CustomFormInput
				inputProps={{
					width: '100%',
					id: 'companyName-onboarding',
					size: '$4',
					borderWidth: 2,
					placeholder: 'Denumire companie',
					textContentType: 'organizationName'
				}}
				errorText={errors.companyName && touched.companyName ? errors.companyName : null}
				onChangeText={handleChange('companyName')}
				onBlur={handleBlur('companyName')}
				value={values.companyName}
			/>
			<CustomFormInput
				inputProps={{
					width: '100%',
					id: 'phone-company-onboarding',
					size: '$4',
					borderWidth: 2,
					placeholder: 'Telefon',
					textContentType: 'telephoneNumber'
				}}
				errorText={errors.companyPhone && touched.companyPhone ? errors.companyPhone : null}
				onChangeText={handleChange('companyPhone')}
				onBlur={handleBlur('companyPhone')}
				value={values.companyPhone}
			/>

			<CustomFormInput
				inputProps={{
					width: '100%',
					id: 'lastNameRepresentative-onboarding',
					size: '$4',
					borderWidth: 2,
					placeholder: 'Nume reprezentant',
					textContentType: 'name'
				}}
				errorText={errors.lastNameRepresentative && touched.lastNameRepresentative ? errors.lastNameRepresentative : null}
				onChangeText={handleChange('lastNameRepresentative')}
				onBlur={handleBlur('lastNameRepresentative')}
				value={values.lastNameRepresentative}
			/>
			<CustomFormInput
				inputProps={{
					width: '100%',
					id: 'firstNameRepresentative-onboarding',
					size: '$4',
					borderWidth: 2,
					placeholder: 'Prenume reprezentant',
					textContentType: 'name'
				}}
				errorText={errors.firstNameRepresentative && touched.firstNameRepresentative ? errors.firstNameRepresentative : null}
				onChangeText={handleChange('firstNameRepresentative')}
				onBlur={handleBlur('firstNameRepresentative')}
				value={values.firstNameRepresentative}
			/>
			{!isOnboarding && (
				<YStack alignSelf="flex-start">
					<Text>Adresă curentă</Text>
					<Text>{userInfo?.address}</Text>
				</YStack>
			)}
			<GoogleSearchInput
				userType={UserType.COMPANY}
				setUserSubscriptionDetails={setUserSubscriptionDetails}
				inputProps={{
					width: '100%',
					id: 'address-company-onboarding',
					size: '$4',
					borderWidth: 2,
					placeholder: 'Adresă companie',
					textContentType: 'fullStreetAddress'
				}}
				value={values?.companyAddress}
				errorText={errors.companyAddress && touched.companyAddress ? errors.companyAddress : null}
				onBlur={handleBlur('companyAddress')}
			/>

			<CustomFormInput
				inputProps={{
					width: '100%',
					id: 'companyPostalCode-onboarding',
					size: '$4',
					borderWidth: 2,
					placeholder: 'Cod poștal',
					textContentType: 'postalCode'
				}}
				errorText={errors.companyPostalCode && touched.companyPostalCode ? errors.companyPostalCode : null}
				onChangeText={handleChange('companyPostalCode')}
				onBlur={handleBlur('companyPostalCode')}
				value={values.companyPostalCode}
			/>
			{isOnboarding && (
				<>
					<SwitchWithLabel
						size="$2"
						label="Utilizez un cont de firmă pentru plată"
						checked={values.taxIdCollection}
						onCheckedChange={(value) => setFieldValue('taxIdCollection', value)}
					/>
					<Text fontSize={12}>În cazul în care vei achiziționa un abonament pe firmă, va trebui să completezi CUI-ul companiei pentru factură.</Text>
				</>
			)}
			{values.taxIdCollection ||
				(!isOnboarding && (
					<CustomFormInput
						inputProps={{
							width: '100%',
							id: 'taxId-onboarding',
							size: '$4',
							borderWidth: 2,
							placeholder: 'CUI',
							textContentType: 'none'
						}}
						errorText={errors.taxId && touched.taxId ? errors.taxId : null}
						onChangeText={handleChange('taxId')}
						onBlur={handleBlur('taxId')}
						value={values.taxId}
					/>
				))}
		</>
	);
};

export default OnboardingCompanyInputs;

const styles = StyleSheet.create({});
