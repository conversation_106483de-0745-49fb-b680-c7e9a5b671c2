import React from 'react';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import { Input, InputProps, Label, LabelProps, Text, View, ViewProps } from 'tamagui';
import { c } from '../../utils/styleVariables';
import { useFormikContext } from 'formik';
import { useOnboarding } from '../../providers/OnboardingProvider';
import { UserType } from '../../utils/enums';
import { useUserInfo } from '../../providers/UserInfoProvider';

const GoogleSearchInput = ({
	inputProps,
	labelProps,
	labelText,
	errorText,
	onBlur,
	value,
	userType,
	setUserSubscriptionDetails
}: {
	inputProps: InputProps;
	labelProps?: LabelProps;
	labelText?: string;
	errorText?: string | null;
	onBlur: (e: any) => void;
	value?: string;
	userType: UserType;
	setUserSubscriptionDetails: (details: any) => void;
}) => {
	const { setFieldValue } = useFormikContext();

	return (
		<View width={'100%'} height={'auto'} flex={1}>
			{!!labelText && <Label {...labelProps}>{labelText}</Label>}
			<GooglePlacesAutocomplete
				placeholder={inputProps.placeholder || ''}
				onPress={(data, details = null) => {
					// 'details' is provided when fetchDetails = true

					setFieldValue(userType === UserType.CLIENT ? 'address' : 'companyAddress', details?.formatted_address, true);
					setFieldValue('lat', details?.geometry.location.lat, true);
					setFieldValue('lng', details?.geometry.location.lng, true);

					const administrativeArea = details?.address_components.find((component) => component.types.includes('administrative_area_level_1'));
					setFieldValue('administrativeArea', administrativeArea?.long_name, true);

					const country = details?.address_components.find((component) => component.types.includes('country'));
					const city = details?.address_components.find((component) => component.types.includes('locality'));
					const street = details?.address_components.find((component) => component.types.includes('route'));
					const streetNumber = details?.address_components.find((component) => component.types.includes('street_number'));
					const postalCode = details?.address_components.find((component) => component.types.includes('postal_code'));
					if (postalCode) {
						setFieldValue('companyPostalCode', postalCode?.long_name);
					}

					setUserSubscriptionDetails &&
						setUserSubscriptionDetails({
							country: country?.short_name,
							city: city?.long_name,
							street: street?.long_name,
							streetNumber: streetNumber?.long_name,
							postalCode: postalCode?.long_name,
							state: administrativeArea?.long_name
						});
				}}
				fetchDetails={true}
				query={{
					key: 'AIzaSyB_0VYgSr15VoeppmqLur_6LeHHxU0q0NI',
					language: 'en',
					components: 'country:ro'
				}}
				textInputProps={{
					InputComp: Input,
					width: '100%',
					id: 'address-onboarding',
					size: '$4',
					borderWidth: 2,
					placeholder: 'Adresă',
					textContentType: 'fullStreetAddress',
					backgroundColor: 'red',
					onBlur
				}}
			/>
			{!!errorText && (
				<Text alignSelf="flex-start" color={c.cancelRed}>
					{errorText}
				</Text>
			)}
		</View>
	);
};

export default GoogleSearchInput;
