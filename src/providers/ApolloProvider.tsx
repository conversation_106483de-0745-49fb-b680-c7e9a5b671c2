import React, { useEffect } from 'react';
import { ApolloProvider, ApolloClient, ApolloLink, createHttpLink, InMemoryCache } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import env from 'react-native-config';
import { useAuth } from './AuthProvider';
import { Platform } from 'react-native';

const Render = ({ children }: any) => {
	// Auth hooks

	const apiURL = Platform.OS === 'ios' ? env.GATEWAY_URL : env.GATEWAY_URL_ANDROID;

	const { getTokens } = useAuth();

	// Create authentication middleware to add id_token to requests
	const authLink = setContext(async (_, { headers }: any) => {
		const tokens = await getTokens();

		// return the headers to the context so httpLink can read them
		return {
			headers: {
				...headers,
				authorization: `Bearer ${tokens?.jwt.toString()}`
			}
		};
	});

	// Handle errors client side
	const errorLink = onError(({ graphQLErrors, networkError }: any) => {
		if (graphQLErrors) {
			console.log('GraphQL Error: ', graphQLErrors);
		}
		if (networkError) {
			console.log('Network Error: ', networkError);
		}
	});
	useEffect(() => {
		getTokens().then((tokens) => console.log('Tokens: ', tokens.jwt.toString()));
	}, []);
	// Create Http link
	const link = createHttpLink({
		uri: apiURL
	});

	// create an inmemory cache instance for caching graphql data
	const cache = new InMemoryCache({
		addTypename: false
	});

	// Create apollo client
	const client = new ApolloClient({
		link: ApolloLink.from([errorLink, authLink, link]),
		cache
	});

	// do not render provider until apiURL is initialised
	if (!apiURL) {
		return null;
	}

	return <ApolloProvider client={client}>{children}</ApolloProvider>;
};

export default Render;
