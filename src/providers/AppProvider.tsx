import React, { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { AppContextProps, FeatureFlags } from '../common/interfaces';
import remoteConfig from '@react-native-firebase/remote-config';
import config from '../common/config';

// @ts-expect-error
export const AppContext = createContext<AppContextProps>({});
export const useAppConfig = (): AppContextProps => useContext(AppContext);

const AppProvider = ({ children }: any) => {
	const [currentConfig, setCurrentConfig] = useState<FeatureFlags>(config);

	useEffect(() => {
		remoteConfig()
			.setDefaults({
				...config
			})
			.then(() => remoteConfig().fetchAndActivate())
			.then(() => {
				const fetchedConfig = remoteConfig().getString('production');
				const parsedConfig = JSON.parse(fetchedConfig);

				setCurrentConfig((previousConfig) => ({ ...previousConfig, ...parsedConfig }));
			})
			.catch(() => console.error('Error on fetching remote config'));

		remoteConfig().setConfigSettings({
			minimumFetchIntervalMillis: 300000
		});
	}, []);

	const values = useMemo(
		() => ({
			featureFlags: currentConfig
		}),
		[currentConfig]
	);

	return <AppContext.Provider value={values}>{children}</AppContext.Provider>;
};

export default AppProvider;
