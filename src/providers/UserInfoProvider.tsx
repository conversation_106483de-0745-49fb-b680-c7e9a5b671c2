import { useApolloClient } from '@apollo/client';

import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { GET_USER_INFO } from '../queries/GET_USER_INFO';
import { useAuth } from './AuthProvider';

import { UserType } from '../utils/enums';
import { CompleteUserInfo, OfferedService, UserInfoContextProps } from '../common/interfaces';
import { GET_USER_SERVICES } from '../queries/GET_USER_SERVICES';

// @ts-expect-error
export const UserInfoContext = createContext<UserInfoContextProps>({});
export const useUserInfo = (): UserInfoContextProps => useContext(UserInfoContext);

const UserInfoProvider = ({ children }: any) => {
	const [userInfo, setUserInfo] = useState<CompleteUserInfo | null>(null);
	const [loading, setLoading] = useState(true);
	const [userServices, setUserServices] = useState<OfferedService[]>([]);
	const { user } = useAuth();
	const client = useApolloClient();

	const getUserInfo = useCallback(() => {
		client
			.query({
				query: GET_USER_INFO,
				fetchPolicy: 'network-only',
				notifyOnNetworkStatusChange: true,
				variables: {
					userId: user?.id
				}
			})
			.then(async ({ data }) => {
				const { getUser } = data;

				if (getUser.status === '200' && user) {
					console.log('getUser', getUser);
					if (getUser.user?.userType === UserType.CLIENT) {
						setUserInfo({
							...user,
							acceptedTC: getUser.user.acceptedTC,
							userType: getUser.user.userType,
							firstName: getUser.user.firstName,
							lastName: getUser.user.lastName,
							phone: getUser.user.phone,
							address: getUser.user.address,
							coordinates: getUser.user.coordinates,
							administrativeArea: getUser.user.administrativeArea
						});
					} else if (getUser.user?.userType === UserType.COMPANY) {
						setUserInfo({
							...user,
							acceptedTC: getUser.user.acceptedTC,
							userType: getUser.user.userType,
							companyName: getUser.user.companyName,
							representativeFirstName: getUser.user.representativeFirstName,
							representativeLastName: getUser.user.representativeLastName,
							phone: getUser.user.phone,
							address: getUser.user.address,
							subscriptionType: getUser.user.subscriptionType,
							country: getUser.user.country,
							city: getUser.user.city,
							street: getUser.user.street,
							streetNumber: getUser.user.streetNumber,
							postalCode: getUser.user.postalCode,
							state: getUser.user.state,
							taxId: getUser.user.taxId,
							coordinates: getUser.user.coordinates,
							administrativeArea: getUser.user.administrativeArea
						});
					}
				} else {
					console.log('Error getting user info');
				}
				setLoading(false);
			})
			.catch((err: any) => {
				console.log('Error getting user info', err);

				setLoading(false);
			});
	}, [client, user]);

	const getUserServices = () => {
		client
			.query({
				query: GET_USER_SERVICES,
				fetchPolicy: 'network-only',
				notifyOnNetworkStatusChange: true,
				variables: {
					userId: user?.id
				}
			})
			.then(async ({ data }) => {
				const { getUserServices } = data;

				if (getUserServices.status === '200') {
					setUserServices(getUserServices.services);
				} else {
					console.log('Error getting user info');
				}
				setLoading(false);
			})
			.catch((err: any) => {
				console.log('Error getting user info', err);

				setLoading(false);
			});
	};

	const setUserProps = useCallback(
		(newProps: Partial<CompleteUserInfo>) => {
			setUserInfo({ ...(userInfo as CompleteUserInfo), ...newProps });
		},
		[userInfo]
	);

	useEffect(() => {
		if (user?.id) {
			getUserInfo();
		}
	}, [user?.id, getUserInfo]);
	useEffect(() => {
		if (userInfo?.id) {
			getUserServices();
		}
	}, [userInfo?.id]);

	const values = useMemo(
		() => ({
			userInfo,
			loadingUserInfo: loading,
			getUserInfo,
			setUserProps,
			userServices
		}),
		[userInfo, loading, getUserInfo, setUserProps, userServices]
	);

	return <UserInfoContext.Provider value={values}>{children}</UserInfoContext.Provider>;
};

export default UserInfoProvider;
