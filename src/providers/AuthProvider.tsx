import {
  autoSignIn,
  confirmSignUp,
  ConfirmSignUpInput,
  deleteUser,
  fetchAuthSession,
  fetchUserAttributes,
  resendSignUpCode,
  signIn,
  SignInInput,
  signInWithRedirect,
  signOut,
  signUp,
} from 'aws-amplify/auth';
import {Hub} from 'aws-amplify/utils';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  AuthContextProps,
  SignUpParameters,
  UserData,
} from '../common/interfaces';

// import {log, recordError, setUserId} from '../crashlytics/crashlytics';
import {mapIDPUserFields} from '../utils/mappers';
// import auth from '@react-native-firebase/auth';
// @ts-expect-error
export const AuthContext = createContext<AuthContextProps>({});
export const useAuth = (): AuthContextProps => useContext(AuthContext);

const AuthProvider = ({children}: any) => {
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(false);
  const [tempUser, setTempUser] = useState({});
  console.log('user', user);
  useEffect(() => {
    // log('AuthProvider mounted');
    const unsubscribe = Hub.listen('auth', async ({payload: {event}}) => {
      console.log('event', event);
      switch (event) {
        case 'signedIn':
          // might need to be changed to a different method
          fetchUserAttributes()
            .then(async currentUser => {
              console.log('listener: currentUser', currentUser);

              setUser(mapIDPUserFields(currentUser));
              //   setUserId(currentUser.sub);
            })
            .catch((err: any) => {
              setUser(null);
              console.log('listener: error getting user attributes', err);

              //   recordError(err, 'Error at sign in');
            });
          break;
        case 'signedOut':
          setUser(null);
          break;
      }
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    fetchUserAttributes()
      .then(async currentUser => {
        console.log('currentUser', currentUser);
        setUser(mapIDPUserFields(currentUser));
        // setUserId(currentUser.sub);
      })
      .catch(err => {
        console.log('error getting user attributes', err);
        setUser(null);
        // recordError(err, 'Error at getting current authenticated user');
      });
  }, []);

  const loginWithProvider = useCallback(
    async (provider: any) => {
      if (loading) {
        return;
      }
      setLoading(true);
      try {
        await signInWithRedirect({provider}).catch(err => {
          console.log('federated error', err);
          // recordError(err, 'Federated sign in error');
        });
      } catch (err: any) {
        console.log('Sign In error', err);
        // recordError(err, 'Sign In error');
      }
      setLoading(false);
    },
    [loading],
  );
  const logout = useCallback(async () => {
    await signOut();
    // await auth().signOut();
  }, []);
  const deleteCognitoAccount = useCallback(async () => {
    await deleteUser();
  }, []);
  const getTokens = useCallback(async () => {
    try {
      const {tokens} = await fetchAuthSession();

      const jwt = tokens?.accessToken || '';

      return {jwt};
    } catch (err: any) {
      console.log('Error getting Cognito tokens', err);
      // recordError(err, 'Error getting Cognito tokens');
    }
  }, []);

  const signUpWithEmailPassword = async ({
    password,
    email,
  }: SignUpParameters) => {
    try {
      const {isSignUpComplete, userId, nextStep} = await signUp({
        username: email,
        password,
        options: {
          userAttributes: {
            email,
          },
          autoSignIn: true,
        },
      });

      console.log(userId);
      console.log('next step ', nextStep);
      console.log('is complete ', isSignUpComplete);

      // CONFIRM_SIGN_UP - The sign up needs to be confirmed by collecting a code from the user and calling confirmSignUp.
      // DONE - The sign up process has been fully completed.
      // COMPLETE_AUTO_SIGN_IN - The sign up process needs to complete by invoking the autoSignIn API.
      if (nextStep.signUpStep === 'COMPLETE_AUTO_SIGN_IN') {
        await autoSignIn();
      }
      return {isSignUpComplete, userId, nextStep};
    } catch (error: any) {
      let errorMessage;
      switch (error.name) {
        case 'UsernameExistsException':
          errorMessage = 'Utilizator deja existent';
          break;
        case 'InvalidPasswordException':
          errorMessage =
            'Parola introdusă trebuie să conțină cel puțin 8 caractere';
          break;
        default:
          errorMessage = 'Eroare la înregistrare. Te rugăm încearcă din nou';
      }
      console.log('error signing up:', error);
      return {hasError: true, errorMessage};
    }
  };

  const handleSignUpConfirmation = async ({
    username,
    confirmationCode,
  }: ConfirmSignUpInput) => {
    try {
      const {isSignUpComplete, nextStep} = await confirmSignUp({
        username,
        confirmationCode,
        options: {
          autoSignIn: true,
        },
      });
      console.log('isSignUpComplete', isSignUpComplete);
      console.log('nextStep', nextStep);

      if (nextStep.signUpStep === 'COMPLETE_AUTO_SIGN_IN') {
        await autoSignIn();
      }
      if (nextStep.signUpStep === 'DONE' && !user && tempUser) {
        await handleSignIn(tempUser);
        setTempUser({});
      }

      return {isSignUpComplete, nextStep};
      // CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED - The user was created with a temporary password and must set a new one. Complete the process with confirmSignIn.
      // CONFIRM_SIGN_IN_WITH_CUSTOM_CHALLENGE - The sign-in must be confirmed with a custom challenge response. Complete the process with confirmSignIn.
      // CONFIRM_SIGN_IN_WITH_TOTP_CODE - The sign-in must be confirmed with a TOTP code from the user. Complete the process with confirmSignIn.
      // CONTINUE_SIGN_IN_WITH_TOTP_SETUP - The TOTP setup process must be continued. Complete the process with confirmSignIn.
      // CONFIRM_SIGN_IN_WITH_SMS_CODE - The sign-in must be confirmed with a SMS code from the user. Complete the process with confirmSignIn.
      // CONTINUE_SIGN_IN_WITH_MFA_SELECTION - The user must select their mode of MFA verification before signing in. Complete the process with confirmSignIn.
      // RESET_PASSWORD - The user must reset their password via resetPassword.
      // CONFIRM_SIGN_UP - The user hasn't completed the sign-up flow fully and must be confirmed via confirmSignUp.
      // DONE - The sign in process has been completed.
    } catch (error) {
      resendSignUpCode({username});
      console.log('error confirming sign up', error);
      return {
        hasError: true,
        errorMessage:
          'Codul introdus este invalid. Un nou cod a fost trimis la adresa ta de email. Te rugăm să încerci din nou.',
      };
    }
  };
  console.log('user', user);
  const handleSignIn = async ({username, password}: SignInInput) => {
    try {
      const {isSignedIn, nextStep} = await signIn({username, password});
      console.log('signing in', isSignedIn);
      console.log('next step', nextStep);
      if (nextStep.signInStep === 'CONFIRM_SIGN_UP') {
        setTempUser({username, password});

        resendSignUpCode({username});
      }
      return {isSignedIn, nextStep};
    } catch (error) {
      console.log('error signing in', error);
      return {hasError: true, errorMessage: 'Utilizator sau parolă incorecte'};
    }
  };

  console.log('user', user);
  const values = useMemo(
    () => ({
      user,
      loading,
      loginWithProvider,
      logout,
      deleteCognitoAccount,
      getTokens,
      signUpWithEmailPassword,
      handleSignUpConfirmation,
      handleSignIn,
    }),
    [
      user,
      loading,
      loginWithProvider,
      logout,
      getTokens,
      deleteCognitoAccount,
      signUpWithEmailPassword,
      handleSignUpConfirmation,
      handleSignIn,
    ],
  );

  return <AuthContext.Provider value={values}>{children}</AuthContext.Provider>;
};

export default AuthProvider;
