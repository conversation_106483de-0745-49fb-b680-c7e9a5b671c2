import React, { createContext, useContext, useMemo, useState } from 'react';
import { ClientOnboardingInfo, CompanyOnboardingInfo, OnboardingContextProps } from '../common/interfaces';
import { UserType } from '../utils/enums';
import { useAuth } from './AuthProvider';
import { useUserInfo } from './UserInfoProvider';

// @ts-expect-error
export const OnboardingContext = createContext<OnboardingContextProps>({});
export const useOnboarding = (): OnboardingContextProps => useContext(OnboardingContext);

const OnboardingProvider = ({ children }: any) => {
	const [userType, setUserType] = useState<UserType>();
	const [clientOnboardingInfo, setClientOnboardingInfo] = useState<ClientOnboardingInfo | undefined>();
	const [companyOnboardingInfo, setCompanyOnboardingInfo] = useState<CompanyOnboardingInfo | undefined>();
	const [services, setServices] = useState();
	const [onboardingActiveStep, setOnboardingActiveStep] = useState(1);
	const [userSubscriptionDetails, setUserSubscriptionDetails] = useState({});
	console.log('client user info', clientOnboardingInfo);
	// useEffect(() => {
	//   if (userType === UserType.CLIENT && clientOnboardingInfo) {
	//     // pentru CLIENT, dupa setare step 2 si clientOnboardingInfo, facem update user in DB si afisam tutorial

	//   } else if (userType === UserType.COMPANY && services) {
	//     // pentru COMPANY, dupa setare step 4 si services, facem update user in DB si afisam tutorial
	//   }
	// }, [
	//   onboardingActiveStep,
	//   clientOnboardingInfo,
	//   companyOnboardingInfo,
	//   services,
	// ]);

	const values = useMemo(
		() => ({
			userType,
			clientOnboardingInfo,
			setClientOnboardingInfo,
			companyOnboardingInfo,
			setCompanyOnboardingInfo,
			services,
			onboardingActiveStep,
			setUserType,
			setServices,
			setOnboardingActiveStep,
			userSubscriptionDetails,
			setUserSubscriptionDetails
		}),
		[
			userType,
			clientOnboardingInfo,
			setClientOnboardingInfo,
			companyOnboardingInfo,
			setCompanyOnboardingInfo,
			services,
			onboardingActiveStep,
			setUserType,
			setServices,
			setOnboardingActiveStep,
			userSubscriptionDetails,
			setUserSubscriptionDetails
		]
	);

	return <OnboardingContext.Provider value={values}>{children}</OnboardingContext.Provider>;
};

export default OnboardingProvider;
