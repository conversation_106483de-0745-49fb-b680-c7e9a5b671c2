import { JWT, SignInInput } from 'aws-amplify/auth';
import { Screens, SubscriptionType, UserType } from '../utils/enums';
import { Dispatch, SetStateAction } from 'react';

export interface UserData {
	id: string;
	email: string;
	email_verified: string;
}

export interface AuthContextProps {
	user: UserData | null;
	loading: boolean;
	loginWithProvider: (provider: 'Amazon' | 'Apple' | 'Facebook' | 'Google') => Promise<void>;
	handleSignIn: ({ username, password }: SignInInput) => Promise<{
		isSignedIn?: boolean;
		nextStep?: {
			signInStep: string;
		};
		hasError?: boolean;
		errorMessage?: string;
	}>;
	logout: () => Promise<void>;
	deleteCognitoAccount: () => Promise<void>;
	getTokens: () => Promise<{ jwt: JWT | string } | void>;
	signUpWithEmailPassword: ({ email, password }: { email: string; password: string }) => Promise<{
		isSignUpComplete?: boolean;
		userId?: string;
		nextStep?: {
			codeDeliveryDetails?: {
				attributeName?: string;
				deliveryMedium?: string;
				destination?: string;
			};
			signUpStep: string;
		};
		hasError?: boolean;
		errorMessage?: string;
	}>;
	handleSignUpConfirmation: ({ username, confirmationCode }: { username: string; confirmationCode: string }) => Promise<{
		isSignUpComplete?: boolean;
		nextStep?: {
			signUpStep: string;
		};
		hasError?: boolean;
		errorMessage?: string;
	}>;
}

export type SignUpParameters = {
	password: string;
	email: string;
};

export interface SignUpForm {
	email: string;
	password: string;
	confirmPassword: string;
}

export interface SignInForm {
	email: string;
	password: string;
}

export interface ConfirmSignupParams {
	username: string;
}

export interface ServiceWidgetParams {
	name: string;
	address: string;
	location: {
		lat: string;
		long: string;
	};
}

export interface ProfileWidgetProps {
	name: string;
	icon?: any;
	navigateToScreen?: Screens;
	onPress?: () => void;
}
export interface UserSubscriptionDetails {
	// Extra details for subscription
	country?: string;
	city?: string;
	street?: string;
	streetNumber?: string;
	postalCode?: string;
	state?: string;
}
export interface OnboardingContextProps {
	userType?: UserType;
	clientOnboardingInfo: ClientOnboardingInfo;
	setClientOnboardingInfo: Dispatch<SetStateAction<ClientOnboardingInfo>>;
	companyOnboardingInfo: CompanyOnboardingInfo;
	setCompanyOnboardingInfo: Dispatch<SetStateAction<CompanyOnboardingInfo>>;
	services?: [];
	setUserType: Dispatch<SetStateAction<UserType | undefined>>;
	setServices: Dispatch<SetStateAction<undefined>>;
	onboardingActiveStep: number;
	setOnboardingActiveStep: Dispatch<SetStateAction<number>>;
	userSubscriptionDetails: UserSubscriptionDetails;
	setUserSubscriptionDetails: Dispatch<SetStateAction<UserSubscriptionDetails>>;
}

export interface ClientOnboardingInfo {
	lastName: string;
	firstName: string;
	phone: string;
	address: string;
	coordinates: {
		lat: string;
		lng: string;
	};
	administrativeArea: string;
}
export interface CompanyOnboardingInfo {
	companyPhone: string;
	companyAddress: string;
	companyName: string;
	lastNameRepresentative: string;
	firstNameRepresentative: string;
	subscriptionType: SubscriptionType;
	coordinates: {
		lat: string;
		lng: string;
	};
	administrativeArea: string;
	taxId: string;
}

export interface OfferedService {
	serviceTypeId: string;
	serviceName: string;
	startTime: string;
	endTime: string;
	allowedCarMakes: [string];
	subserviceTypeIds: [string];
	price: number;
}
export interface CompleteUserInfo extends UserData {
	acceptedTC?: string;
	userType?: string;
	// Client
	firstName?: string;
	lastName?: string;
	// Company
	companyName?: string;
	representativeFirstName?: string;
	representativeLastName?: string;
	subscriptionType?: SubscriptionType;
	services?: OfferedService[];
	appointments?: number;
	country?: string;
	city?: string;
	street?: string;
	streetNumber?: string;
	postalCode?: string;
	state?: string;
	taxId?: string;
	// Common
	phone?: string;
	address?: string;
	coordinates?: {
		lat: string;
		lng: string;
	};
	administrativeArea: string;
}

export interface UserInfoContextProps {
	userInfo: CompleteUserInfo | null;
	loadingUserInfo: boolean;
	getUserInfo: () => void;
	setUserProps: (newProps: Partial<CompleteUserInfo>) => void;
	userServices: OfferedService[];
}

export interface FeatureFlags {
	MONTHLY_SUBSCRIPTION_PRICE_ID: string;
	YEARLY_SUBSCRIPTION_PRICE_ID: string;
}

export interface AppContextProps {
	featureFlags: FeatureFlags;
}
